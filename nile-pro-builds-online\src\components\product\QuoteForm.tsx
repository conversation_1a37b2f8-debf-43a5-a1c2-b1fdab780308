/**
 * Quote Form Component
 * Handles quote requests with validation and Strapi integration
 */

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Send, CheckCircle, AlertCircle, User, Mail, Phone, Building, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSubmitQuote } from '@/hooks/useProducts';
import { StrapiProduct } from '@/services/productService';

interface QuoteFormProps {
  product: StrapiProduct;
  className?: string;
  onSuccess?: () => void;
}

interface QuoteFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message?: string;
}

const QuoteForm = ({ product, className = "", onSuccess }: QuoteFormProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const submitQuoteMutation = useSubmitQuote();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<QuoteFormData>();

  const onSubmit = async (data: QuoteFormData) => {
    try {
      const quoteData = {
        ...data,
        productId: product.id,
        productName: product.attributes.name,
      };

      const result = await submitQuoteMutation.mutateAsync(quoteData);

      if (result.success) {
        setIsSubmitted(true);
        reset();
        onSuccess?.();

        // Reset success state after 5 seconds
        setTimeout(() => setIsSubmitted(false), 5000);
      }
    } catch (error) {
      console.error('Quote submission error:', error);
    }
  };

  if (isSubmitted) {
    return (
      <div className={`transition-all duration-300 ${className}`}>
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6 text-center">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-900 mb-2">
              Quote Request Submitted!
            </h3>
            <p className="text-green-700 mb-4">
              Thank you for your interest in {product.attributes.name}.
              Our team will contact you within 24 hours with a detailed quote.
            </p>
            <div className="text-sm text-green-600">
              <p>Reference: QR-{Date.now().toString().slice(-6)}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-blue-600" />
          Request a Quote
        </CardTitle>
        <p className="text-sm text-gray-600">
          Get a personalized quote for {product.attributes.name}
        </p>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              Full Name *
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              {...register('name', {
                required: 'Name is required',
                minLength: { value: 2, message: 'Name must be at least 2 characters' }
              })}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              Email Address *
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email address"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Please enter a valid email address'
                }
              })}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Phone Field */}
          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              Phone Number
            </Label>
            <Input
              id="phone"
              type="tel"
              placeholder="Enter your phone number"
              {...register('phone')}
            />
          </div>

          {/* Company Field */}
          <div className="space-y-2">
            <Label htmlFor="company" className="flex items-center gap-2">
              <Building className="w-4 h-4" />
              Company Name
            </Label>
            <Input
              id="company"
              type="text"
              placeholder="Enter your company name"
              {...register('company')}
            />
          </div>

          {/* Message Field */}
          <div className="space-y-2">
            <Label htmlFor="message">
              Project Details & Requirements
            </Label>
            <Textarea
              id="message"
              rows={4}
              placeholder="Tell us about your project requirements, timeline, quantity needed, or any specific questions..."
              {...register('message')}
            />
          </div>

          {/* Error Alert */}
          {submitQuoteMutation.isError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to submit quote request. Please try again or contact us directly.
              </AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isSubmitting || submitQuoteMutation.isPending}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSubmitting || submitQuoteMutation.isPending ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Submitting...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Send className="w-4 h-4" />
                Send Quote Request
              </div>
            )}
          </Button>

          {/* Privacy Notice */}
          <p className="text-xs text-gray-500 text-center">
            By submitting this form, you agree to our privacy policy.
            We'll only use your information to provide you with a quote and relevant product information.
          </p>
        </form>
      </CardContent>
    </Card>
  );
};

export default QuoteForm;
