/**
 * <PERSON><PERSON>t to set up public permissions for Strapi API endpoints
 * Run this script after starting Strapi to enable public access to API endpoints
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';

async function setupPermissions() {
  try {
    console.log('🚀 Setting up Strapi permissions...');
    
    // First, let's check if we can access the admin API
    // Note: This script assumes you have admin access or the permissions are already set
    
    console.log('📋 Manual Setup Instructions:');
    console.log('');
    console.log('1. Open Strapi Admin Panel: http://localhost:1337/admin');
    console.log('2. Go to Settings > Users & Permissions Plugin > Roles');
    console.log('3. Click on "Public" role');
    console.log('4. In the Permissions section, find "Product" and enable:');
    console.log('   ✅ find');
    console.log('   ✅ findOne');
    console.log('   ✅ seed (if available)');
    console.log('   ✅ clear (if available)');
    console.log('');
    console.log('5. Find "Brand" and enable:');
    console.log('   ✅ find');
    console.log('   ✅ findOne');
    console.log('');
    console.log('6. Find "Quote-request" and enable:');
    console.log('   ✅ create');
    console.log('');
    console.log('7. Click "Save" button');
    console.log('');
    console.log('🎯 After completing these steps, your API endpoints will be accessible!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Alternative: Try to set up permissions via API (requires authentication)
async function setupPermissionsViaAPI() {
  try {
    console.log('🔧 Attempting automatic setup...');
    
    // This would require authentication, which is complex to set up automatically
    // For now, we'll provide manual instructions
    
    console.log('⚠️  Automatic setup requires admin authentication.');
    console.log('📋 Please follow the manual setup instructions above.');
    
  } catch (error) {
    console.error('❌ Automatic setup failed:', error.message);
    console.log('📋 Please follow the manual setup instructions above.');
  }
}

if (require.main === module) {
  setupPermissions();
}
