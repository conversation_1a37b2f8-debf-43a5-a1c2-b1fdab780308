"use strict";
// import type { Core } from '@strapi/strapi';
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Set up public permissions for API endpoints
 */
async function setupPublicPermissions(strapi) {
    try {
        console.log('Setting up public permissions...');
        // Find the public role
        const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
            where: { type: 'public' }
        });
        if (!publicRole) {
            console.log('Public role not found');
            return;
        }
        // Define permissions to enable
        const permissionsToEnable = [
            // Product permissions
            { controller: 'product', action: 'find' },
            { controller: 'product', action: 'findOne' },
            { controller: 'product', action: 'seed' },
            { controller: 'product', action: 'clear' },
            // Brand permissions
            { controller: 'brand', action: 'find' },
            { controller: 'brand', action: 'findOne' },
            // Quote request permissions
            { controller: 'quote-request', action: 'create' },
        ];
        for (const permission of permissionsToEnable) {
            try {
                // Check if permission already exists
                const existingPermission = await strapi.query('plugin::users-permissions.permission').findOne({
                    where: {
                        role: publicRole.id,
                        action: `api::${permission.controller}.${permission.controller}.${permission.action}`
                    }
                });
                if (!existingPermission) {
                    // Create the permission
                    await strapi.query('plugin::users-permissions.permission').create({
                        data: {
                            role: publicRole.id,
                            action: `api::${permission.controller}.${permission.controller}.${permission.action}`,
                            enabled: true
                        }
                    });
                    console.log(`✅ Enabled permission: ${permission.controller}.${permission.action}`);
                }
                else {
                    // Update existing permission to be enabled
                    await strapi.query('plugin::users-permissions.permission').update({
                        where: { id: existingPermission.id },
                        data: { enabled: true }
                    });
                    console.log(`✅ Updated permission: ${permission.controller}.${permission.action}`);
                }
            }
            catch (error) {
                console.log(`❌ Error setting permission ${permission.controller}.${permission.action}:`, error.message);
            }
        }
        console.log('✅ Public permissions setup completed');
    }
    catch (error) {
        console.error('❌ Error setting up public permissions:', error);
    }
}
exports.default = {
    /**
     * An asynchronous register function that runs before
     * your application is initialized.
     *
     * This gives you an opportunity to extend code.
     */
    register( /* { strapi }: { strapi: Core.Strapi } */) { },
    /**
     * An asynchronous bootstrap function that runs before
     * your application gets started.
     *
     * This gives you an opportunity to set up your data model,
     * run jobs, or perform some special logic.
     */
    async bootstrap({ strapi }) {
        // Set up public permissions for API endpoints
        await setupPublicPermissions(strapi);
    },
};
