# Comprehensive Project Brief: Product Card and Product Details Pages

## Executive Summary

This project brief provides complete specifications for building modern product card and product details pages for the Nile Pro MEP website using React and Strapi CMS. The implementation will replace existing product pages with a superior user experience, improved UI/UX design, and seamless CMS integration.

### Project Objectives
- Create responsive, modern product card components for product listings
- Develop comprehensive product details pages with enhanced functionality
- Integrate with Strapi headless CMS for content management
- Implement superior UI/UX design compared to existing solutions
- Ensure mobile-first responsive design
- Optimize for performance and SEO

### Key Deliverables
1. Modern product card component with hover effects and key specifications
2. Detailed product page with image gallery, specifications, and quote form
3. Strapi CMS content structure and API integration
4. Responsive design system with consistent styling
5. Performance-optimized React components
6. Complete removal and replacement of old product pages

## Current State Analysis

### Existing Website Assessment
The current Nile Pro website (https://www.nile-pro.com/) shows a 404 error for the products page, indicating that the product display functionality needs to be built from scratch. The website features:

- Professional MEP services company branding
- Blue and white color scheme with industrial aesthetic
- Partner brands section (ACS Klima, HiRef, DKC Europe)
- Service-focused content structure

### Reference Analysis
The ACS Klima website (https://www.acsklima.com/condensing-unit/) provides a basic reference with:
- Simple product image display
- Basic technical specifications
- Contact form for quotes
- Minimal design with room for improvement

### Improvement Opportunities
- Enhanced visual design with modern UI patterns
- Better information hierarchy and content organization
- Improved mobile responsiveness
- Advanced filtering and search capabilities
- Rich media galleries and interactive elements
- Streamlined quote request process



## UI/UX Design Specifications

### Design Philosophy
The design approach emphasizes modern, professional, and industrial aesthetics that reflect engineering excellence and technical precision. The interface prioritizes clean, minimalist design with focus on usability and clear information hierarchy.

### Color Palette

#### Primary Colors
- **Primary Blue**: #2563EB - Used for primary actions, links, and brand elements
- **Secondary Blue**: #1E40AF - Used for darker accents and hover states
- **Light Blue**: #DBEAFE - Used for backgrounds and subtle highlights

#### Neutral Colors
- **Dark Gray**: #1F2937 - Primary text color for headings and important content
- **Medium Gray**: #6B7280 - Secondary text color for descriptions and metadata
- **Light Gray**: #F3F4F6 - Background color for cards and sections
- **White**: #FFFFFF - Main background color for content areas

#### Status Colors
- **Success Green**: #10B981 - Indicates available products and successful actions
- **Warning Orange**: #F59E0B - Shows limited stock and attention-required items
- **Error Red**: #EF4444 - Displays out of stock items and error states

### Typography System

#### Font Families
- **Primary Font**: Inter, system-ui, sans-serif - Used for all interface text
- **Technical Font**: Roboto Mono - Used specifically for technical specifications

#### Font Scale
- **H1 (Product Titles)**: 32px, Font Weight 700 - Main product headings
- **H2 (Section Headers)**: 24px, Font Weight 600 - Section titles
- **H3 (Subsections)**: 20px, Font Weight 600 - Subsection headings
- **Body Text**: 16px, Font Weight 400 - Standard content text
- **Small Text**: 14px, Font Weight 400 - Metadata and secondary information
- **Technical Specifications**: 14px, Font Weight 500, Roboto Mono - Spec tables

### Layout System

#### Grid Structure
- **Container Maximum Width**: 1200px - Ensures optimal reading width
- **Grid System**: 12-column responsive grid for flexible layouts
- **Gutter Spacing**: 24px between grid columns
- **Page Margins**: 16px (mobile), 24px (tablet), 32px (desktop)

#### Spacing Scale
- **Extra Small**: 4px - Fine adjustments and tight spacing
- **Small**: 8px - Close related elements
- **Medium**: 16px - Standard component spacing
- **Large**: 24px - Section spacing
- **Extra Large**: 32px - Major section breaks
- **Double Extra Large**: 48px - Page section divisions

### Component Specifications

#### Product Card Design
- **Card Dimensions**: 320px width with automatic height adjustment
- **Border Radius**: 12px for modern, friendly appearance
- **Shadow**: 0 4px 6px rgba(0, 0, 0, 0.1) with enhanced shadow on hover
- **Hover Animation**: Lift effect (translateY(-4px)) with smooth transition
- **Image Aspect Ratio**: 4:3 for consistent visual rhythm
- **Internal Padding**: 20px for comfortable content spacing

#### Product Details Page Layout
- **Image Gallery**: 60% width on desktop, full width on mobile devices
- **Content Area**: 40% width on desktop with full width on mobile
- **Specifications Table**: Alternating row colors (#F9FAFB) for readability
- **Form Elements**: 48px height with 8px border radius for touch-friendly interaction

### Interactive Elements

#### Button Styles
- **Primary Buttons**: Blue background (#2563EB), white text, 12px border radius
- **Secondary Buttons**: White background with blue border and text
- **Hover States**: 10% darker color with smooth 0.2s ease transition
- **Disabled State**: 50% opacity with cursor restriction

#### Form Elements
- **Input Fields**: Light gray border (#D1D5DB) with blue focus border
- **Focus States**: 2px solid blue outline (#2563EB) for accessibility
- **Validation States**: Green borders for success, red for errors
- **Placeholder Text**: Medium gray (#6B7280) for subtle guidance

### Responsive Design

#### Breakpoint System
- **Mobile**: Less than 768px - Single column layout, touch-optimized
- **Tablet**: 768px to 1024px - Two-column layout with adjusted spacing
- **Desktop**: Greater than 1024px - Full multi-column layout

#### Mobile Optimizations
- Touch-friendly button sizes (minimum 44px)
- Simplified navigation with collapsible menus
- Optimized image sizes for faster loading
- Swipe-enabled image galleries

### Accessibility Standards

#### Visual Accessibility
- **Contrast Ratio**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Focus Indicators**: Visible 2px solid blue outline (#2563EB)
- **Color Independence**: Information not conveyed by color alone

#### Interaction Accessibility
- **Keyboard Navigation**: Full tab navigation support
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Alternative Text**: Descriptive alt text for all product images


## Strapi CMS Content Structure

### Content Architecture Overview

The Strapi CMS implementation requires three primary content types with carefully designed relationships to support both product card displays and detailed product pages. The structure emphasizes flexibility, scalability, and ease of content management.

### Primary Content Types

#### Product Content Type (Collection)

The Product content type serves as the core entity containing all product information needed for both card displays and detailed pages.

**Basic Information Fields**
- **title** (Text, Required): The display name of the product, used in cards and page headers
- **slug** (UID, Required): URL-friendly identifier automatically generated from title
- **description** (Rich Text, Required): Supports both short descriptions for cards and full descriptions for detail pages
- **category** (Relation to Category, Required): Links to product category for filtering and navigation
- **brand** (Relation to Brand, Required): Links to manufacturer brand for filtering and brand display

**Media Management Fields**
- **featured_image** (Media, Required): Primary product image displayed in cards and hero sections, recommended size 800x600px
- **gallery** (Media, Multiple): Additional product images for detail page galleries, recommended size 1200x900px
- **technical_drawings** (Media, Multiple): Technical diagrams and schematics in PDF or high-resolution image format

**Specifications System**
- **specifications** (Component, Repeatable): Flexible specification system with the following sub-fields:
  - **attribute** (Text, Required): Specification name (e.g., "Cooling Capacity")
  - **value** (Text, Required): Specification value (e.g., "500")
  - **unit** (Text, Optional): Unit of measurement (e.g., "kW")
  - **category** (Enumeration): Groups specifications into "Performance", "Physical", "Electrical", "Environmental"

**Pricing and Availability**
- **price** (Decimal, Optional): Product price when displayed, can be null for "Contact for Price" scenarios
- **currency** (Enumeration): Supports "USD", "EUR", "EGP" with USD as default
- **availability_status** (Enumeration, Required): Options include "In Stock", "Limited Stock", "Out of Stock", "Contact for Availability"

**Technical Documentation**
- **datasheets** (Component, Repeatable): Technical documentation with sub-fields:
  - **title** (Text, Required): Document name for display
  - **file** (Media, Required): PDF file attachment
  - **description** (Text, Optional): Brief description of document contents
- **manuals** (Component, Repeatable): User manuals with sub-fields:
  - **title** (Text, Required): Manual name
  - **file** (Media, Required): PDF file attachment
  - **type** (Enumeration): Categorizes as "Installation", "Operation", "Maintenance"

**SEO and Metadata**
- **meta_title** (Text, Optional): SEO title, defaults to product title if not specified
- **meta_description** (Text, Optional): SEO description, defaults to short description
- **keywords** (Text, Optional): Comma-separated keywords for search optimization

**Publishing Controls**
- **published_at** (DateTime): Publication date, null value indicates draft status
- **featured** (Boolean): Marks product as featured for homepage display, defaults to false

#### Category Content Type (Collection)

The Category content type organizes products into logical groupings and supports hierarchical navigation structures.

**Basic Category Information**
- **name** (Text, Required, Unique): Category display name such as "Condensing Units" or "Heat Pumps"
- **slug** (UID, Required): URL-friendly identifier for routing and filtering
- **description** (Rich Text, Optional): Category description for category pages
- **icon** (Media, Optional): Category icon or representative image

**Hierarchical Structure**
- **parent_category** (Relation to Category, Optional): Self-referencing relationship enabling nested category structures

**Display Management**
- **sort_order** (Integer): Controls display order in navigation menus, defaults to 0
- **is_active** (Boolean): Controls category visibility, defaults to true

#### Brand Content Type (Collection)

The Brand content type manages manufacturer information and supports brand-based filtering and display.

**Brand Information**
- **name** (Text, Required, Unique): Brand name such as "ACS Klima" or "HiRef"
- **slug** (UID, Required): URL-friendly identifier for filtering and routing
- **description** (Rich Text, Optional): Brand description and background information
- **logo** (Media, Optional): Brand logo for display in product cards and brand sections

**Brand Details**
- **website** (Text, Optional): Official brand website URL
- **country** (Text, Optional): Country of origin for brand context
- **established_year** (Integer, Optional): Year established for brand credibility

**Display Controls**
- **is_featured** (Boolean): Controls appearance in featured brands section, defaults to false
- **sort_order** (Integer): Controls display order in brand listings, defaults to 0

### Content Relationships

#### Relationship Structure
- **Product to Category**: Many-to-One relationship allowing each product to belong to one category
- **Product to Brand**: Many-to-One relationship linking each product to its manufacturer
- **Product to Related Products**: Many-to-Many self-referencing relationship for cross-selling
- **Category Hierarchy**: Many-to-One self-referencing relationship for nested categories

### API Endpoint Structure

#### Product Endpoints
The API provides comprehensive endpoints for product data retrieval and management:

- `GET /api/products` - Retrieves product listings with filtering, sorting, and pagination
- `GET /api/products/:slug` - Fetches single product by slug with full relationship data
- `GET /api/products/:id` - Retrieves single product by ID for administrative purposes
- `POST /api/products` - Creates new product (administrative access required)
- `PUT /api/products/:id` - Updates existing product (administrative access required)
- `DELETE /api/products/:id` - Removes product (administrative access required)

#### Supporting Endpoints
- `GET /api/categories` - Lists all categories with hierarchical structure
- `GET /api/categories/:slug` - Retrieves category with associated products
- `GET /api/brands` - Lists all brands with basic information
- `GET /api/brands/:slug` - Fetches brand with associated products

### Query Parameters and Filtering

#### Product Listing Filters
The API supports comprehensive filtering options for product listings:

- **category**: Filter by category slug for category-specific listings
- **brand**: Filter by brand slug for brand-specific products
- **featured**: Boolean filter for featured products (true/false)
- **availability**: Filter by availability status for inventory management
- **search**: Text search across product titles and descriptions
- **sort**: Sort field options include title, price, created_at
- **order**: Sort direction as ascending (asc) or descending (desc)
- **limit**: Number of results per page for pagination
- **start**: Pagination offset for result sets

#### Example API Usage
```
GET /api/products?category=condensing-units&brand=acs-klima&limit=12
GET /api/products?featured=true&limit=6
GET /api/products?search=cooling&sort=title&order=asc
```

### Content Population Strategy

#### Initial Data Requirements
The CMS requires strategic content population to support immediate functionality:

1. **Category Structure**: 8-10 main categories covering HVAC, Electrical, Plumbing, and specialized MEP equipment
2. **Brand Portfolio**: 5-8 partner brands including ACS Klima, HiRef, DKC Europe, and other established manufacturers
3. **Product Catalog**: 20-30 representative products distributed across categories to demonstrate functionality
4. **Specification Data**: Comprehensive technical specifications for each product to showcase the specification system
5. **Media Assets**: High-quality product images, technical drawings, and documentation files

#### Content Migration Considerations
When migrating from existing systems, the content structure supports flexible data mapping and import processes. The specification component system accommodates varying technical data formats, while the media management system handles diverse file types and sizes.


## Technical Implementation Guide

### Technology Stack and Architecture

#### Frontend Technology Stack
The React-based frontend utilizes modern development practices and proven libraries to ensure maintainable, performant code:

- **React 18+**: Latest React version with functional components, hooks, and concurrent features
- **React Router v6**: Client-side routing with nested routes and dynamic parameters
- **Axios**: HTTP client for API communication with request/response interceptors
- **Tailwind CSS**: Utility-first CSS framework for rapid, consistent styling
- **React Query (TanStack Query)**: Advanced data fetching, caching, and synchronization
- **React Hook Form**: Performant form handling with minimal re-renders
- **Framer Motion**: Animation library for smooth transitions and micro-interactions

#### Backend Integration
- **Strapi v4**: Headless CMS providing RESTful API and admin interface
- **PostgreSQL/MySQL**: Relational database for structured data storage
- **Cloudinary/AWS S3**: Cloud media storage for images and documents

### Project Structure and Organization

The project follows a modular architecture that separates concerns and promotes code reusability:

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Shared components (Header, Footer, Loading)
│   ├── product/         # Product-specific components
│   └── ui/              # Base UI components (Button, Input, Modal)
├── pages/               # Route-level page components
├── hooks/               # Custom React hooks for data fetching
├── services/            # API communication and business logic
├── utils/               # Helper functions and constants
└── styles/              # Global styles and Tailwind configuration
```

### Core Implementation Components

#### API Service Layer

The centralized API service manages all Strapi communications with proper error handling and authentication:

```javascript
// services/api.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: { 'Content-Type': 'application/json' },
});

// Authentication and error handling interceptors
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('strapiToken');
  if (token) config.headers.Authorization = `Bearer ${token}`;
  return config;
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);
```

#### Product Service Implementation

The product service provides specialized methods for product data operations:

```javascript
// services/productService.js
export const productService = {
  getProducts: async (params = {}) => {
    const queryParams = new URLSearchParams();
    
    // Population for related data
    queryParams.append('populate[0]', 'featured_image');
    queryParams.append('populate[1]', 'category');
    queryParams.append('populate[2]', 'brand');
    queryParams.append('populate[3]', 'specifications');
    
    // Dynamic filtering
    if (params.category) queryParams.append('filters[category][slug][$eq]', params.category);
    if (params.brand) queryParams.append('filters[brand][slug][$eq]', params.brand);
    if (params.search) {
      queryParams.append('filters[$or][0][title][$containsi]', params.search);
      queryParams.append('filters[$or][1][description][$containsi]', params.search);
    }
    
    const response = await api.get(`/products?${queryParams.toString()}`);
    return response.data;
  },

  getProductBySlug: async (slug) => {
    // Comprehensive population for product details
    const queryParams = new URLSearchParams();
    queryParams.append('filters[slug][$eq]', slug);
    queryParams.append('populate[0]', 'featured_image');
    queryParams.append('populate[1]', 'gallery');
    queryParams.append('populate[2]', 'category');
    queryParams.append('populate[3]', 'brand');
    queryParams.append('populate[4]', 'specifications');
    queryParams.append('populate[5]', 'datasheets');
    queryParams.append('populate[6]', 'manuals');
    queryParams.append('populate[7]', 'related_products.featured_image');
    
    const response = await api.get(`/products?${queryParams.toString()}`);
    return response.data.data[0];
  }
};
```

#### Custom Hooks for Data Management

React Query hooks provide efficient data fetching with caching and error handling:

```javascript
// hooks/useProducts.js
import { useQuery } from '@tanstack/react-query';
import { productService } from '../services/productService';

export const useProducts = (params = {}) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => productService.getProducts(params),
    staleTime: 5 * 60 * 1000,      // 5 minutes
    cacheTime: 10 * 60 * 1000,     // 10 minutes
    keepPreviousData: true,         // Smooth pagination
  });
};

export const useProduct = (slug) => {
  return useQuery({
    queryKey: ['product', slug],
    queryFn: () => productService.getProductBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000,
  });
};
```

### Component Implementation Details

#### Product Card Component

The product card component provides a consistent, interactive display for product listings:

```javascript
// components/product/ProductCard.jsx
import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const ProductCard = ({ product }) => {
  const { slug, title, description, featured_image, category, brand, specifications, availability_status, price } = product.attributes;

  const getStatusColor = (status) => {
    const colors = {
      'In Stock': 'bg-green-100 text-green-800',
      'Limited Stock': 'bg-yellow-100 text-yellow-800',
      'Out of Stock': 'bg-red-100 text-red-800',
      default: 'bg-blue-100 text-blue-800'
    };
    return colors[status] || colors.default;
  };

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className="bg-white rounded-xl shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
    >
      <Link to={`/products/${slug}`} className="block">
        {/* Product Image with Status Badge */}
        <div className="relative aspect-[4/3] overflow-hidden">
          <img
            src={featured_image?.data?.attributes?.url}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            loading="lazy"
          />
          <div className="absolute top-3 right-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(availability_status)}`}>
              {availability_status}
            </span>
          </div>
        </div>

        {/* Product Information */}
        <div className="p-5">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-blue-600 font-medium">
              {brand?.data?.attributes?.name}
            </span>
            <span className="text-xs text-gray-500">
              {category?.data?.attributes?.name}
            </span>
          </div>

          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
            {title}
          </h3>

          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {description}
          </p>

          {/* Key Specifications Display */}
          {specifications?.data?.slice(0, 3).map((spec, index) => (
            <div key={index} className="flex justify-between text-xs text-gray-600 mb-1">
              <span>{spec.attribute}</span>
              <span className="font-medium">{spec.value} {spec.unit}</span>
            </div>
          ))}

          {/* Price and Action Button */}
          <div className="flex items-center justify-between mt-4">
            {price ? (
              <span className="text-lg font-bold text-blue-600">
                ${price.toLocaleString()}
              </span>
            ) : (
              <span className="text-sm text-gray-500">Contact for Price</span>
            )}
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
              View Details
            </button>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};
```

#### Product Details Page Implementation

The product details page provides comprehensive product information with interactive elements:

```javascript
// pages/ProductDetailsPage.jsx
import React, { useState } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useProduct } from '../hooks/useProduct';

const ProductDetailsPage = () => {
  const { slug } = useParams();
  const { data: product, isLoading, error } = useProduct(slug);
  const [activeTab, setActiveTab] = useState('specifications');

  if (isLoading) return <Loading />;
  if (error) return <div>Error loading product</div>;
  if (!product) return <div>Product not found</div>;

  const { title, description, featured_image, gallery, category, brand, specifications } = product.attributes;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li><Link to="/" className="text-gray-500 hover:text-gray-700">Home</Link></li>
              <li><span className="text-gray-400">/</span></li>
              <li><Link to="/products" className="text-gray-500 hover:text-gray-700">Products</Link></li>
              <li><span className="text-gray-400">/</span></li>
              <li className="text-gray-900 font-medium">{title}</li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Product Content Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product Gallery */}
          <div>
            <ProductGallery featuredImage={featured_image} gallery={gallery} productTitle={title} />
          </div>

          {/* Product Information */}
          <div>
            <div className="mb-4">
              <span className="text-blue-600 font-medium">{brand?.data?.attributes?.name}</span>
              <span className="text-gray-400 mx-2">•</span>
              <span className="text-gray-600">{category?.data?.attributes?.name}</span>
            </div>

            <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
            <div className="prose prose-gray max-w-none mb-8">
              <p>{description}</p>
            </div>

            <QuoteForm productId={product.id} productTitle={title} />
          </div>
        </div>

        {/* Tabbed Content Section */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {['specifications', 'documentation', 'downloads'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'specifications' && <ProductSpecs specifications={specifications} />}
            {activeTab === 'documentation' && <TechnicalDocumentation />}
            {activeTab === 'downloads' && <DownloadSection />}
          </div>
        </div>
      </div>
    </div>
  );
};
```

#### Quote Form Component

The quote form provides a streamlined process for customer inquiries:

```javascript
// components/product/QuoteForm.jsx
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { productService } from '../../services/productService';

const QuoteForm = ({ productId, productTitle }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await productService.submitQuoteRequest({
        ...data,
        product: productId,
        product_title: productTitle,
        submitted_at: new Date().toISOString()
      });
      setSubmitStatus('success');
      reset();
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Request a Quote</h3>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
            <input
              type="text"
              {...register('name', { required: 'Name is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
            <input
              type="email"
              {...register('email', { 
                required: 'Email is required',
                pattern: { value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, message: 'Invalid email' }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
          </div>
        </div>

        <textarea
          rows={4}
          {...register('message')}
          placeholder="Tell us about your project requirements..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {isSubmitting ? 'Submitting...' : 'Send Quote Request'}
        </button>
      </form>
    </div>
  );
};
```

### Performance Optimization Strategies

#### Image Optimization
- Implement lazy loading for all product images to improve initial page load times
- Use responsive images with multiple sizes for different screen resolutions
- Optimize images through Strapi media library or integrate with CDN services
- Implement progressive image loading with blur-up technique

#### Code Splitting and Lazy Loading
- Implement route-based code splitting to reduce initial bundle size
- Lazy load components that are not immediately visible (modals, tabs)
- Use React.lazy() and Suspense for dynamic imports
- Split vendor libraries into separate chunks for better caching

#### Caching Strategy
- Utilize React Query's intelligent caching for API responses
- Implement service worker for offline functionality and asset caching
- Configure appropriate cache headers for static assets
- Use browser storage for user preferences and form data

#### SEO Optimization
- Generate dynamic meta tags for each product page
- Implement structured data (JSON-LD) for product information
- Create XML sitemap for all product pages
- Optimize page loading speed for better search rankings


## Implementation Instructions

### Development Environment Setup

#### Prerequisites and Dependencies
Before beginning implementation, ensure the development environment includes:

1. **Node.js 18+** with npm or yarn package manager
2. **Strapi v4** instance with PostgreSQL or MySQL database
3. **Git** for version control and collaboration
4. **Code editor** with React and JavaScript support (VS Code recommended)

#### Project Initialization Steps

**Step 1: Create React Application**
```bash
npx create-react-app nile-pro-products
cd nile-pro-products
npm install axios @tanstack/react-query react-router-dom react-hook-form framer-motion
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

**Step 2: Configure Tailwind CSS**
Update `tailwind.config.js` with the project's design system:
```javascript
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: { 500: '#2563EB', 600: '#1E40AF' },
        gray: { 50: '#F9FAFB', 100: '#F3F4F6', 600: '#6B7280', 900: '#1F2937' }
      },
      fontFamily: { sans: ['Inter', 'system-ui', 'sans-serif'] }
    },
  },
  plugins: [],
}
```

**Step 3: Set Up Strapi CMS**
```bash
npx create-strapi-app@latest nile-pro-cms --quickstart
cd nile-pro-cms
npm run develop
```

**Step 4: Configure Environment Variables**
Create `.env` file in React project root:
```
REACT_APP_STRAPI_URL=http://localhost:1337
REACT_APP_API_TOKEN=your_strapi_api_token
```

### Strapi Configuration Steps

#### Content Type Creation
Navigate to Strapi admin panel and create content types following the specifications:

1. **Product Content Type**: Create with all specified fields including media, specifications component, and relationships
2. **Category Content Type**: Set up with hierarchical structure and display controls
3. **Brand Content Type**: Configure with brand information and media fields
4. **Quote Request Content Type**: Create for form submissions with email notifications

#### API Permissions Configuration
Configure API permissions in Strapi admin:
- Set public read access for Products, Categories, and Brands
- Restrict write access to authenticated users only
- Configure CORS settings for React application domain

#### Media Library Setup
Configure media library for optimal performance:
- Set up responsive image formats (webp, jpg, png)
- Configure image optimization settings
- Set up CDN integration if using external storage

### Component Development Sequence

#### Phase 1: Core Infrastructure (Week 1)
1. **API Service Layer**: Implement centralized API communication with error handling
2. **Custom Hooks**: Create React Query hooks for data fetching and caching
3. **Base UI Components**: Develop reusable Button, Input, Modal, and Loading components
4. **Routing Setup**: Configure React Router with protected routes and navigation

#### Phase 2: Product Display Components (Week 2)
1. **Product Card Component**: Implement with hover effects, status badges, and responsive design
2. **Product Grid Component**: Create with filtering, sorting, and pagination functionality
3. **Product Gallery Component**: Develop image gallery with zoom and navigation features
4. **Product Specifications Component**: Build dynamic specification display with categorization

#### Phase 3: Product Details and Forms (Week 3)
1. **Product Details Page**: Implement comprehensive product page with tabbed content
2. **Quote Form Component**: Create with validation, error handling, and success feedback
3. **Related Products Component**: Develop cross-selling product recommendations
4. **Breadcrumb Navigation**: Implement hierarchical navigation with category support

#### Phase 4: Advanced Features and Optimization (Week 4)
1. **Search and Filtering**: Implement advanced search with category and brand filters
2. **Performance Optimization**: Add lazy loading, code splitting, and caching strategies
3. **SEO Implementation**: Configure meta tags, structured data, and sitemap generation
4. **Testing and Quality Assurance**: Implement unit tests and end-to-end testing

### Testing Strategy and Quality Assurance

#### Unit Testing Implementation
Create comprehensive unit tests for all components:

```javascript
// __tests__/ProductCard.test.js
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProductCard from '../components/product/ProductCard';

const mockProduct = {
  attributes: {
    slug: 'test-product',
    title: 'Test Product',
    description: 'Test description',
    featured_image: { data: { attributes: { url: 'test.jpg' } } },
    availability_status: 'In Stock'
  }
};

test('renders product card with correct information', () => {
  render(
    <BrowserRouter>
      <ProductCard product={mockProduct} />
    </BrowserRouter>
  );
  
  expect(screen.getByText('Test Product')).toBeInTheDocument();
  expect(screen.getByText('Test description')).toBeInTheDocument();
  expect(screen.getByText('In Stock')).toBeInTheDocument();
});
```

#### Integration Testing
Test component interactions and data flow:
- API integration with mock responses
- Form submission and validation
- Navigation and routing functionality
- Responsive design across breakpoints

#### End-to-End Testing
Implement user workflow testing:
- Product browsing and filtering
- Product detail page navigation
- Quote form submission process
- Mobile device compatibility

### Migration Strategy from Old Product Pages

#### Content Audit and Mapping
1. **Inventory Existing Content**: Catalog all current product information, images, and documentation
2. **Data Structure Mapping**: Map existing data fields to new Strapi content structure
3. **Media Asset Organization**: Organize and optimize existing product images and documents
4. **URL Structure Planning**: Plan URL redirects to maintain SEO rankings

#### Migration Execution Plan

**Phase 1: Parallel Development**
- Develop new product pages alongside existing system
- Import content into Strapi CMS without affecting live site
- Test functionality with subset of products

**Phase 2: Content Migration**
- Bulk import product data using Strapi import tools
- Migrate and optimize media assets
- Set up URL redirects from old product pages

**Phase 3: Gradual Rollout**
- Implement feature flags for controlled rollout
- A/B test new pages against existing ones
- Monitor performance metrics and user feedback

**Phase 4: Complete Transition**
- Switch all product traffic to new pages
- Remove old product page code and assets
- Update internal links and navigation

#### SEO Preservation Strategy
- Implement 301 redirects for all old product URLs
- Maintain existing meta tags and structured data
- Monitor search rankings during transition
- Update XML sitemap with new URL structure

### Deployment and Production Considerations

#### Environment Configuration
Set up production environment variables:
```
REACT_APP_STRAPI_URL=https://cms.nile-pro.com
REACT_APP_API_TOKEN=production_api_token
REACT_APP_ENVIRONMENT=production
```

#### Build Optimization
Configure production build settings:
- Enable code splitting and tree shaking
- Optimize bundle size with webpack-bundle-analyzer
- Configure service worker for caching
- Set up CDN for static asset delivery

#### Monitoring and Analytics
Implement comprehensive monitoring:
- Error tracking with Sentry or similar service
- Performance monitoring with Web Vitals
- User analytics with Google Analytics 4
- API performance monitoring

#### Security Considerations
- Implement Content Security Policy headers
- Configure HTTPS for all communications
- Sanitize user inputs in forms
- Regular security updates for dependencies

### Maintenance and Future Enhancements

#### Regular Maintenance Tasks
- Monitor and update dependencies monthly
- Review and optimize database queries
- Update content and product information
- Monitor site performance and user feedback

#### Planned Future Enhancements
1. **Advanced Search**: Implement Elasticsearch for complex product searches
2. **Personalization**: Add user accounts and personalized product recommendations
3. **Inventory Integration**: Connect with inventory management systems
4. **Multi-language Support**: Implement internationalization for global markets
5. **Mobile App**: Develop React Native mobile application

### Success Metrics and KPIs

#### Performance Metrics
- Page load time under 3 seconds
- Core Web Vitals scores in green range
- Mobile responsiveness score above 95%
- SEO performance maintaining or improving rankings

#### User Experience Metrics
- Bounce rate reduction of 20% or more
- Increased time on product pages
- Higher quote form conversion rates
- Improved mobile user engagement

#### Business Impact Metrics
- Increased product inquiry volume
- Higher quality leads from quote forms
- Improved customer satisfaction scores
- Enhanced brand perception and trust

This comprehensive implementation guide provides all necessary information for successfully building and deploying modern product card and product details pages that will significantly enhance the Nile Pro website's functionality and user experience.

