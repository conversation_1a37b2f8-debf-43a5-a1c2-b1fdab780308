'use strict';

/**
 * product controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::product.product', ({ strapi }) => ({
  // Add custom methods here
  async importSources(ctx) {
    try {
      const sources = [
        {
          name: 'ACS Klima',
          domain: 'acsklima.com',
          description: 'Turkish HVAC equipment manufacturer'
        }
      ];

      ctx.send({
        success: true,
        data: sources
      });
    } catch (error) {
      ctx.send({
        success: false,
        error: error.message
      }, 500);
    }
  },

  async importProduct(ctx) {
    try {
      const { url } = ctx.request.body;

      ctx.send({
        success: true,
        message: 'Import endpoint working!',
        data: { url }
      });
    } catch (error) {
      ctx.send({
        success: false,
        error: error.message
      }, 500);
    }
  },

  async seed(ctx) {
    try {
      const seedService = strapi.service('api::product.seed');
      const result = await seedService.seedProducts();

      ctx.send(result);
    } catch (error) {
      console.error('Seed error:', error);
      ctx.send({
        success: false,
        error: error.message
      }, 500);
    }
  },

  async clear(ctx) {
    try {
      const seedService = strapi.service('api::product.seed');
      const result = await seedService.clearProducts();

      ctx.send(result);
    } catch (error) {
      console.error('Clear error:', error);
      ctx.send({
        success: false,
        error: error.message
      }, 500);
    }
  }
}));
