import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Corporate from "./pages/Corporate";
import MissionVision from "./pages/MissionVision";
import ModernProductsPage from "./pages/ModernProductsPage";
import BrandPage from "./pages/BrandPage";
import ModernProductDetail from "./pages/ModernProductDetail";
import Solutions from "./pages/Solutions";
import References from "./pages/References";
import ContactPage from "./pages/ContactPage";
import AdminPage from "./pages/AdminPage";
import NotFound from "./pages/NotFound";
import WhatsAppFloat from "./components/WhatsAppFloat";
import { initPerformanceMonitoring } from "./utils/performance";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
});

// Initialize performance monitoring
if (typeof window !== 'undefined') {
  initPerformanceMonitoring();
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/corporate" element={<Corporate />} />
          <Route path="/mission-vision" element={<MissionVision />} />
          <Route path="/products" element={<ModernProductsPage />} />
          <Route path="/brands/:brandId" element={<BrandPage />} />
          <Route path="/products/:slug" element={<ModernProductDetail />} />
          <Route path="/solutions" element={<Solutions />} />
          <Route path="/solutions/:category" element={<Solutions />} />
          <Route path="/references" element={<References />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/admin" element={<AdminPage />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        <WhatsAppFloat />
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
