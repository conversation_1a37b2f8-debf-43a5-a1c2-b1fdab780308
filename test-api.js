/**
 * Simple API test script
 */

const axios = require('axios');

async function testAPI() {
  const baseURL = 'http://localhost:1337';
  
  console.log('🧪 Testing Strapi API endpoints...\n');
  
  // Test basic Strapi health
  try {
    console.log('1. Testing Strapi health...');
    const healthResponse = await axios.get(`${baseURL}/_health`);
    console.log('✅ Strapi is running');
  } catch (error) {
    console.log('❌ Strapi health check failed:', error.message);
    return;
  }
  
  // Test admin API
  try {
    console.log('\n2. Testing admin API...');
    const adminResponse = await axios.get(`${baseURL}/admin`);
    console.log('✅ Admin panel accessible');
  } catch (error) {
    console.log('❌ Admin API failed:', error.message);
  }
  
  // Test products API
  try {
    console.log('\n3. Testing products API...');
    const productsResponse = await axios.get(`${baseURL}/api/products`);
    console.log('✅ Products API working');
    console.log('📊 Response:', productsResponse.data);
  } catch (error) {
    console.log('❌ Products API failed:', error.response?.status, error.response?.data || error.message);
  }
  
  // Test content-manager API (internal Strapi API)
  try {
    console.log('\n4. Testing content-manager API...');
    const contentResponse = await axios.get(`${baseURL}/content-manager/collection-types/api::product.product`);
    console.log('✅ Content manager API working');
  } catch (error) {
    console.log('❌ Content manager API failed:', error.response?.status, error.message);
  }
  
  console.log('\n🔍 Diagnosis:');
  console.log('If products API is failing but content-manager works, it\'s a permissions issue.');
  console.log('If both are failing, it\'s a content type configuration issue.');
}

testAPI().catch(console.error);
