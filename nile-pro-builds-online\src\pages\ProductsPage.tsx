import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { brands } from '@/data/brands';
import { products } from '@/data/products';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Grid,
  List,
  Star,
  Heart,
  Eye,
  Download,
  ExternalLink,
  Building2,
  Globe
} from 'lucide-react';

const ProductsPage = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Handle URL parameters
  useEffect(() => {
    const brandParam = searchParams.get('brand');
    if (brandParam) {
      setSelectedBrand(brandParam);
    }
  }, [searchParams]);

  // Transform products to match the expected format
  const transformedProducts = products.map(product => ({
    id: product.id,
    brandId: product.brandId || 'generic',
    name: product.title,
    model: product.specifications?.find(spec => spec.name === 'Model')?.value || 'N/A',
    category: product.category,
    image: product.images[0]?.url || '/placeholder.svg',
    price: 0, // Price not available in current data
    currency: 'USD',
    description: product.shortDescription,
    specifications: product.specifications?.reduce((acc, spec) => {
      acc[spec.name] = spec.value + (spec.unit ? ` ${spec.unit}` : '');
      return acc;
    }, {} as Record<string, string>) || {},
    features: product.features?.map(f => f.title) || [],
    availability: 'in-stock',
    rating: 4.5,
    reviews: 10,
    featured: product.tags?.includes('Featured') || false
  }));

  // Filter products based on search and filters
  const filteredProducts = transformedProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBrand = selectedBrand === 'all' || product.brandId === selectedBrand;
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;

    return matchesSearch && matchesBrand && matchesCategory;
  });

  // Group products by brand
  const productsByBrand = brands.map(brand => ({
    ...brand,
    products: filteredProducts.filter(product => product.brandId === brand.id)
  })).filter(brand => brand.products.length > 0);

  // Product categories for filtering
  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'air-handling', name: 'Air Handling Units' },
    { id: 'cooling', name: 'Cooling Systems' },
    { id: 'ventilation', name: 'Ventilation' },
    { id: 'heating', name: 'Heating Systems' },
    { id: 'fan-coil', name: 'Fan Coil Units' },
    { id: 'electrical', name: 'Electrical Systems' },
    { id: 'air-filtration', name: 'Air Filtration' },
    { id: 'heat-pumps', name: 'Heat Pumps' },
    { id: 'exhaust', name: 'Exhaust Units' }
  ];

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Our Products
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Discover premium HVAC and MEP products from world-leading brands.
              Quality equipment for every project requirement.
            </p>
          </div>

          {/* Search and Filters */}
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                  placeholder="Search products, models, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 min-h-[44px]"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="px-4 py-2 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                >
                  <option value="all">All Brands</option>
                  {brands.map(brand => (
                    <option key={brand.id} value={brand.id}>{brand.name}</option>
                  ))}
                </select>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                <div className="flex border border-border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Brands Section */}
      <section className="py-16 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Our <span className="text-primary">Partner Brands</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              We partner with the world's leading manufacturers to bring you the highest quality HVAC and MEP equipment.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {brands.filter(brand => brand.featured).map((brand) => (
              <Card key={brand.id} className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                <CardContent className="p-8 text-center">
                  <div className="w-24 h-24 mx-auto mb-6 bg-white rounded-lg flex items-center justify-center shadow-sm">
                    <img
                      src={brand.logo}
                      alt={`${brand.name} Logo`}
                      className="max-w-20 max-h-20 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                      }}
                    />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2">{brand.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{brand.description}</p>
                  <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground mb-4">
                    <span className="flex items-center">
                      <Globe className="h-3 w-3 mr-1" />
                      {brand.country}
                    </span>
                    <span className="flex items-center">
                      <Building2 className="h-3 w-3 mr-1" />
                      {brand.founded}
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-1 justify-center mb-4">
                    {brand.specialties.slice(0, 2).map((specialty, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setSelectedBrand(brand.id);
                        document.getElementById('products-section')?.scrollIntoView({ behavior: 'smooth' });
                      }}
                    >
                      View Products ({brand.productCount})
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(brand.website, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Products by Brand Section */}
      <section id="products-section" className="py-16 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {productsByBrand.length === 0 ? (
            <div className="text-center py-16">
              <h3 className="text-2xl font-bold text-foreground mb-4">No Products Found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search criteria or browse all products.
              </p>
              <Button onClick={() => {
                setSearchTerm('');
                setSelectedBrand('all');
                setSelectedCategory('all');
              }}>
                Clear Filters
              </Button>
            </div>
          ) : (
            productsByBrand.map((brand) => (
              <div key={brand.id} className="mb-16">
                {/* Brand Header */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center shadow-sm">
                      <img
                        src={brand.logo}
                        alt={`${brand.name} Logo`}
                        className="max-w-12 max-h-12 object-contain"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                    <div>
                      <Link to={`/brands/${brand.id}`}>
                        <h2 className="text-2xl md:text-3xl font-bold text-foreground hover:text-primary transition-colors">
                          {brand.name} Products
                        </h2>
                      </Link>
                      <p className="text-muted-foreground">
                        {brand.products.length} product{brand.products.length !== 1 ? 's' : ''} available
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => window.open(brand.website, '_blank')}
                    className="hidden sm:flex"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Visit Website
                  </Button>
                </div>

                {/* Products Grid */}
                <div className={`grid gap-6 ${viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
                  }`}>
                  {brand.products.map((product) => (
                    <Card key={product.id} className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                      <div className="aspect-video overflow-hidden rounded-t-lg">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder.svg';
                          }}
                        />
                      </div>
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h3 className="text-lg font-bold text-foreground mb-1 line-clamp-2">
                              {product.name}
                            </h3>
                            <p className="text-sm text-muted-foreground mb-2">
                              Model: {product.model}
                            </p>
                          </div>
                          <Badge
                            variant={product.availability === 'in-stock' ? 'default' : 'secondary'}
                            className="ml-2"
                          >
                            {product.availability === 'in-stock' ? 'In Stock' :
                              product.availability === 'pre-order' ? 'Pre-Order' : 'Out of Stock'}
                          </Badge>
                        </div>

                        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                          {product.description}
                        </p>

                        {/* Key Specifications */}
                        <div className="mb-4">
                          <h4 className="text-xs font-semibold text-foreground mb-2">Key Specs:</h4>
                          <div className="space-y-1">
                            {Object.entries(product.specifications).slice(0, 2).map(([key, value]) => (
                              <div key={key} className="flex justify-between text-xs">
                                <span className="text-muted-foreground">{key}:</span>
                                <span className="text-foreground font-medium">{value}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Features */}
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {product.features.slice(0, 3).map((feature, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Rating */}
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm font-medium">{product.rating}</span>
                            <span className="text-xs text-muted-foreground">
                              ({product.reviews} reviews)
                            </span>
                          </div>
                          {product.featured && (
                            <Badge variant="secondary" className="text-xs">
                              Featured
                            </Badge>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <Button size="sm" className="flex-1">
                            <Eye className="h-3 w-3 mr-1" />
                            View Details
                          </Button>
                          <Button variant="outline" size="sm">
                            <Heart className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ProductsPage;
