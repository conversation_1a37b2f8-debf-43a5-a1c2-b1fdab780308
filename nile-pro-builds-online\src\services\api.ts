/**
 * Centralized API service for Strapi CMS integration
 * Handles all communication with the Strapi backend
 */

import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('strapiToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    
    // Handle specific error cases
    if (error.response?.status === 401) {
      localStorage.removeItem('strapiToken');
      // Optionally redirect to login
    }
    
    return Promise.reject(error);
  }
);

export default api;

// Export API base URL for direct use
export { API_BASE_URL };
