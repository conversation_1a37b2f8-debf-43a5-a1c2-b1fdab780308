/**
 * Custom React hooks for product data fetching
 * Uses React Query for caching and state management
 */

import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { productService, ProductQueryParams, QuoteRequest, StrapiProduct } from '../services/productService';

// Query keys for consistent caching
export const QUERY_KEYS = {
  products: 'products',
  product: 'product',
  featuredProducts: 'featuredProducts',
  popularProducts: 'popularProducts',
  relatedProducts: 'relatedProducts',
  categories: 'categories',
  brands: 'brands',
} as const;

/**
 * Hook for fetching products with filtering and pagination
 */
export const useProducts = (params: ProductQueryParams = {}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.products, params],
    queryFn: () => productService.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    keepPreviousData: true, // Smooth pagination
  });
};

/**
 * Hook for fetching a single product by slug
 */
export const useProduct = (slug: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.product, slug],
    queryFn: () => productService.getProductBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry for 404 errors
      if (error?.response?.status === 404) return false;
      return failureCount < 3;
    },
  });
};

/**
 * Hook for fetching a single product by ID
 */
export const useProductById = (id: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.product, id],
    queryFn: () => productService.getProductById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for fetching featured products
 */
export const useFeaturedProducts = (limit: number = 6) => {
  return useQuery({
    queryKey: [QUERY_KEYS.featuredProducts, limit],
    queryFn: () => productService.getFeaturedProducts(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook for fetching popular products
 */
export const usePopularProducts = (limit: number = 6) => {
  return useQuery({
    queryKey: [QUERY_KEYS.popularProducts, limit],
    queryFn: () => productService.getPopularProducts(limit),
    staleTime: 15 * 60 * 1000,
  });
};

/**
 * Hook for fetching related products
 */
export const useRelatedProducts = (
  productId: number,
  category: string,
  brand?: string,
  limit: number = 4
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.relatedProducts, productId, category, brand, limit],
    queryFn: () => productService.getRelatedProducts(productId, category, brand, limit),
    enabled: !!productId && !!category,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for fetching product categories
 */
export const useCategories = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.categories],
    queryFn: () => productService.getCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook for fetching brands
 */
export const useBrands = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.brands],
    queryFn: () => productService.getBrands(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook for submitting quote requests
 */
export const useSubmitQuote = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (quoteData: QuoteRequest) => productService.submitQuoteRequest(quoteData),
    onSuccess: () => {
      // Optionally invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['quote-requests'] });
    },
    onError: (error) => {
      console.error('Quote submission error:', error);
    },
  });
};

/**
 * Hook for prefetching product data
 */
export const usePrefetchProduct = () => {
  const queryClient = useQueryClient();

  return {
    prefetchProduct: (slug: string) => {
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.product, slug],
        queryFn: () => productService.getProductBySlug(slug),
        staleTime: 10 * 60 * 1000,
      });
    },
    prefetchProducts: (params: ProductQueryParams) => {
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.products, params],
        queryFn: () => productService.getProducts(params),
        staleTime: 5 * 60 * 1000,
      });
    },
  };
};

/**
 * Hook for invalidating product cache
 */
export const useInvalidateProducts = () => {
  const queryClient = useQueryClient();

  return {
    invalidateProducts: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.products] });
    },
    invalidateProduct: (slug: string) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.product, slug] });
    },
    invalidateAll: () => {
      queryClient.invalidateQueries();
    },
  };
};

/**
 * Utility hook for search functionality
 */
export const useProductSearch = (searchTerm: string, debounceMs: number = 300) => {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState(searchTerm);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  return useProducts({
    search: debouncedSearchTerm,
    limit: 20,
  });
};

// Re-export types for convenience
export type { StrapiProduct, ProductQueryParams, QuoteRequest };
