/**
 * Data transformation utilities for Strapi CMS integration
 * Converts Strapi API responses to frontend data structures
 */

import { StrapiProduct } from '../services/productService';
import { Product, ProductImage, ProductSpecification, ProductFeature, ProductApplication, ProductDownload } from '../types/product';

/**
 * Transform Strapi product to frontend Product type
 */
export const transformStrapiProduct = (strapiProduct: StrapiProduct): Product => {
  const { id, attributes } = strapiProduct;
  
  // Transform images
  const images: ProductImage[] = attributes.images?.data?.map((img, index) => ({
    id: img.id.toString(),
    url: img.attributes.url.startsWith('http') 
      ? img.attributes.url 
      : `${import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337'}${img.attributes.url}`,
    alt: img.attributes.alternativeText || attributes.name,
    caption: img.attributes.caption || '',
    isMain: index === 0,
    width: img.attributes.width,
    height: img.attributes.height,
  })) || [];

  // Transform specifications
  const specifications: ProductSpecification[] = [];
  if (attributes.specifications && typeof attributes.specifications === 'object') {
    Object.entries(attributes.specifications).forEach(([key, value]) => {
      if (value && typeof value === 'object' && 'value' in value) {
        specifications.push({
          id: key,
          name: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
          value: value.value,
          unit: value.unit || '',
          category: value.category || 'general',
        });
      } else if (value) {
        specifications.push({
          id: key,
          name: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
          value: value.toString(),
          unit: '',
          category: 'general',
        });
      }
    });
  }

  // Add individual specification fields
  if (attributes.capacity) {
    specifications.push({
      id: 'capacity',
      name: 'Capacity',
      value: attributes.capacity,
      unit: '',
      category: 'performance',
    });
  }
  
  if (attributes.powerConsumption) {
    specifications.push({
      id: 'powerConsumption',
      name: 'Power Consumption',
      value: attributes.powerConsumption,
      unit: '',
      category: 'electrical',
    });
  }
  
  if (attributes.efficiency) {
    specifications.push({
      id: 'efficiency',
      name: 'Efficiency',
      value: attributes.efficiency,
      unit: '',
      category: 'performance',
    });
  }
  
  if (attributes.weight) {
    specifications.push({
      id: 'weight',
      name: 'Weight',
      value: attributes.weight,
      unit: '',
      category: 'physical',
    });
  }

  // Transform features
  const features: ProductFeature[] = [];
  if (attributes.features && Array.isArray(attributes.features)) {
    attributes.features.forEach((feature: any, index: number) => {
      if (typeof feature === 'string') {
        features.push({
          id: `feature-${index}`,
          title: feature,
          description: '',
          icon: '',
        });
      } else if (feature && typeof feature === 'object') {
        features.push({
          id: feature.id || `feature-${index}`,
          title: feature.title || feature.name || '',
          description: feature.description || '',
          icon: feature.icon || '',
        });
      }
    });
  }

  // Transform applications (derived from category and features)
  const applications: ProductApplication[] = [
    {
      id: 'primary',
      title: getCategoryDisplayName(attributes.category),
      description: `Suitable for ${getCategoryDisplayName(attributes.category).toLowerCase()} applications`,
      sector: 'HVAC',
      icon: getCategoryIcon(attributes.category),
    }
  ];

  // Transform downloads
  const downloads: ProductDownload[] = [];
  
  if (attributes.brochure?.data) {
    downloads.push({
      id: 'brochure',
      title: 'Product Brochure',
      description: 'Detailed product information and specifications',
      url: attributes.brochure.data.attributes.url.startsWith('http')
        ? attributes.brochure.data.attributes.url
        : `${import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337'}${attributes.brochure.data.attributes.url}`,
      type: 'brochure',
      fileSize: '',
      format: 'PDF',
    });
  }

  if (attributes.manuals?.data) {
    attributes.manuals.data.forEach((manual, index) => {
      downloads.push({
        id: `manual-${index}`,
        title: manual.attributes.name || 'User Manual',
        description: 'Installation and operation manual',
        url: manual.attributes.url.startsWith('http')
          ? manual.attributes.url
          : `${import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337'}${manual.attributes.url}`,
        type: 'manual',
        fileSize: '',
        format: 'PDF',
      });
    });
  }

  // Transform tags
  const tags: string[] = [];
  if (attributes.tags && Array.isArray(attributes.tags)) {
    tags.push(...attributes.tags);
  }
  if (attributes.brand) {
    tags.push(attributes.brand);
  }
  tags.push(getCategoryDisplayName(attributes.category));

  return {
    id: id.toString(),
    title: attributes.name,
    slug: attributes.slug,
    category: attributes.category as any,
    shortDescription: attributes.shortDescription || extractShortDescription(attributes.description),
    fullDescription: attributes.description,
    features,
    specifications,
    applications,
    images,
    downloads,
    tags: [...new Set(tags)], // Remove duplicates
    brand: attributes.brand,
    brandId: attributes.brand?.toLowerCase().replace(/\s+/g, '-'),
    isActive: !!attributes.publishedAt,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
};

/**
 * Extract short description from full description
 */
const extractShortDescription = (fullDescription: string): string => {
  if (!fullDescription) return '';
  
  // Remove HTML tags and get first sentence or 150 characters
  const plainText = fullDescription.replace(/<[^>]*>/g, '');
  const firstSentence = plainText.split('.')[0];
  
  if (firstSentence.length > 150) {
    return plainText.substring(0, 150).trim() + '...';
  }
  
  return firstSentence + '.';
};

/**
 * Get display name for category
 */
const getCategoryDisplayName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'air-handling-unit': 'Air Handling Unit',
    'condensing-unit': 'Condensing Unit',
    'heat-recovery-ventilation-unit': 'Heat Recovery Ventilation Unit',
    'energy-recovery-ventilation-unit': 'Energy Recovery Ventilation Unit',
    'fan-coil-unit': 'Fan Coil Unit',
    'ecology-unit': 'Ecology Unit',
    'water-source-heat-pump': 'Water Source Heat Pump',
    'exhaust-unit': 'Exhaust Unit',
  };
  
  return categoryMap[category] || category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Get icon for category
 */
const getCategoryIcon = (category: string): string => {
  const iconMap: Record<string, string> = {
    'air-handling-unit': 'wind',
    'condensing-unit': 'snowflake',
    'heat-recovery-ventilation-unit': 'recycle',
    'energy-recovery-ventilation-unit': 'battery',
    'fan-coil-unit': 'fan',
    'ecology-unit': 'leaf',
    'water-source-heat-pump': 'droplets',
    'exhaust-unit': 'arrow-up',
  };
  
  return iconMap[category] || 'settings';
};

/**
 * Transform availability status to display format
 */
export const getAvailabilityStatus = (availability: string): {
  label: string;
  color: string;
  bgColor: string;
} => {
  const statusMap: Record<string, { label: string; color: string; bgColor: string }> = {
    'in-stock': {
      label: 'In Stock',
      color: 'text-green-800',
      bgColor: 'bg-green-100',
    },
    'out-of-stock': {
      label: 'Out of Stock',
      color: 'text-red-800',
      bgColor: 'bg-red-100',
    },
    'pre-order': {
      label: 'Pre-Order',
      color: 'text-blue-800',
      bgColor: 'bg-blue-100',
    },
    'discontinued': {
      label: 'Discontinued',
      color: 'text-gray-800',
      bgColor: 'bg-gray-100',
    },
  };
  
  return statusMap[availability] || {
    label: 'Contact for Availability',
    color: 'text-yellow-800',
    bgColor: 'bg-yellow-100',
  };
};

/**
 * Format price for display
 */
export const formatPrice = (price?: number, currency: string = 'USD'): string => {
  if (!price) return 'Contact for Price';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

/**
 * Generate SEO-friendly URL
 */
export const generateProductUrl = (slug: string): string => {
  return `/products/${slug}`;
};
