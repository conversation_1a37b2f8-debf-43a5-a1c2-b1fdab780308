{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": "HVAC and MEP products"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "richtext", "required": true}, "shortDescription": {"type": "text", "maxLength": 500}, "category": {"type": "enumeration", "enum": ["air-handling-unit", "condensing-unit", "heat-recovery-ventilation-unit", "energy-recovery-ventilation-unit", "fan-coil-unit", "ecology-unit", "water-source-heat-pump", "exhaust-unit", "cooling", "ventilation", "heating", "heat-pumps", "electrical", "air-filtration"], "required": true}, "brand": {"type": "relation", "relation": "manyToOne", "target": "api::brand.brand", "inversedBy": "products"}, "brandName": {"type": "string", "maxLength": 100}, "model": {"type": "string", "maxLength": 100}, "price": {"type": "decimal", "min": 0}, "currency": {"type": "string", "default": "USD", "maxLength": 3}, "specifications": {"type": "json"}, "features": {"type": "json"}, "technicalData": {"type": "json"}, "dimensions": {"type": "json"}, "weight": {"type": "string", "maxLength": 50}, "powerConsumption": {"type": "string", "maxLength": 100}, "capacity": {"type": "string", "maxLength": 100}, "efficiency": {"type": "string", "maxLength": 100}, "images": {"type": "media", "multiple": true, "allowedTypes": ["images"]}, "brochure": {"type": "media", "multiple": false, "allowedTypes": ["files"]}, "manuals": {"type": "media", "multiple": true, "allowedTypes": ["files"]}, "certifications": {"type": "json"}, "availability": {"type": "enumeration", "enum": ["in-stock", "out-of-stock", "pre-order", "discontinued", "contact-for-availability"], "default": "in-stock"}, "tags": {"type": "json"}, "applications": {"type": "json"}, "relatedProducts": {"type": "relation", "relation": "manyToMany", "target": "api::product.product"}, "sourceUrl": {"type": "string", "maxLength": 500}, "sourceWebsite": {"type": "string", "maxLength": 100}, "importedAt": {"type": "datetime"}, "metaTitle": {"type": "string", "maxLength": 255}, "metaDescription": {"type": "text", "maxLength": 500}, "featured": {"type": "boolean", "default": false}, "popular": {"type": "boolean", "default": false}}}