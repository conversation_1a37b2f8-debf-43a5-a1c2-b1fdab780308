import { ArrowRight, Award, Shield, Users } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import heroBackground from '@/assets/hero-background.jpg';

const Hero = () => {
  const navigate = useNavigate();

  return (
    <section id="home" className="relative min-h-[100dvh] sm:min-h-screen flex items-center justify-center overflow-hidden pt-16 sm:pt-0">
      {/* Background Image with Overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${heroBackground})` }}
      >
        <div className="absolute inset-0 bg-gradient-hero opacity-85"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center py-8 sm:py-0">
        <div className="animate-fade-in">
          {/* Main Heading */}
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight tracking-tight">
            <span className="block text-primary-light font-semibold mb-2">Nile Pro</span>
            Engineering
            <span className="block bg-gradient-to-r from-primary-light via-accent to-primary-light bg-clip-text text-transparent animate-pulse">
              Excellence
            </span>
            in MEP Solutions
          </h1>

          {/* Subtitle */}
          <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-200/90 mb-6 sm:mb-8 max-w-4xl mx-auto leading-relaxed font-light">
            Nile Pro delivers comprehensive MEP installation works combining advanced technology
            with durable design for optimal performance in every environment.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-8 sm:mb-16">
            <Button
              size="lg"
              className="w-full sm:w-auto bg-gradient-primary hover:opacity-90 hover:scale-105 shadow-lg shadow-primary/25 text-sm sm:text-base lg:text-lg px-8 sm:px-10 py-4 sm:py-5 min-h-[44px] transition-all duration-300 font-semibold"
              onClick={() => {
                // Scroll to services section
                const servicesSection = document.getElementById('services');
                if (servicesSection) {
                  servicesSection.scrollIntoView({ behavior: 'smooth' });
                } else {
                  // If services section not found, navigate to services page
                  window.location.href = '/solutions';
                }
              }}
            >
              Discover Our Services
              <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button
              size="lg"
              className="w-full sm:w-auto bg-white/10 text-white border-2 border-accent hover:bg-accent hover:text-white hover:scale-105 backdrop-blur-sm transition-all duration-300 text-sm sm:text-base lg:text-lg px-8 sm:px-10 py-4 sm:py-5 min-h-[44px] font-semibold shadow-lg"
              onClick={() => {
                // Navigate to products page
                navigate('/products');
              }}
            >
              View Products
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 max-w-5xl mx-auto">
            <div className="text-center animate-slide-in-right bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300" style={{ animationDelay: '0.2s' }}>
              <div className="flex justify-center mb-4 sm:mb-5">
                <div className="p-3 sm:p-4 bg-gradient-primary rounded-full shadow-lg">
                  <Award className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-white" />
                </div>
              </div>
              <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">9+</div>
              <div className="text-sm sm:text-base lg:text-lg text-gray-300 font-medium">Years Experience</div>
            </div>

            <div className="text-center animate-slide-in-right bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300" style={{ animationDelay: '0.4s' }}>
              <div className="flex justify-center mb-4 sm:mb-5">
                <div className="p-3 sm:p-4 bg-gradient-primary rounded-full shadow-lg">
                  <Shield className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-white" />
                </div>
              </div>
              <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">50</div>
              <div className="text-sm sm:text-base lg:text-lg text-gray-300 font-medium">Projects Completed</div>
            </div>

            <div className="text-center animate-slide-in-right bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300" style={{ animationDelay: '0.6s' }}>
              <div className="flex justify-center mb-4 sm:mb-5">
                <div className="p-3 sm:p-4 bg-gradient-primary rounded-full shadow-lg">
                  <Users className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-white" />
                </div>
              </div>
              <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">100%</div>
              <div className="text-sm sm:text-base lg:text-lg text-gray-300 font-medium">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator - Hidden on mobile, visible on larger screens */}
      <div className="hidden sm:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-float">
        <div className="w-7 h-12 border-2 border-white/60 rounded-full flex justify-center hover:border-accent hover:scale-110 transition-all duration-300 bg-white/5 backdrop-blur-sm">
          <div className="w-1.5 h-4 bg-gradient-to-b from-white to-accent rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;