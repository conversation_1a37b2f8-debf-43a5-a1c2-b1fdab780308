'use strict';

/**
 * Product seeding service
 * Seeds the database with sample product data for development
 */

const sampleProducts = [
  {
    name: 'ACS Klima Air Handling Unit AHU-2500',
    slug: 'acs-klima-ahu-2500',
    description: '<p>High-efficiency air handling unit designed for commercial and industrial applications. Features advanced filtration, heat recovery, and smart controls for optimal energy performance.</p>',
    shortDescription: 'High-efficiency commercial air handling unit with advanced filtration and heat recovery.',
    category: 'air-handling-unit',
    brandName: 'ACS Klima',
    model: 'AHU-2500',
    price: 15000,
    currency: 'USD',
    capacity: '2500 m³/h',
    powerConsumption: '5.5 kW',
    efficiency: 'EER 3.8',
    weight: '450 kg',
    availability: 'in-stock',
    featured: true,
    popular: true,
    specifications: {
      airflow: { value: '2500', unit: 'm³/h', category: 'performance' },
      pressure: { value: '800', unit: 'Pa', category: 'performance' },
      filterClass: { value: 'F7', unit: '', category: 'filtration' },
      heatRecovery: { value: '85', unit: '%', category: 'efficiency' },
      soundLevel: { value: '45', unit: 'dB(A)', category: 'acoustic' }
    },
    features: [
      { title: 'High Efficiency Heat Recovery', description: 'Up to 85% heat recovery efficiency' },
      { title: 'Advanced Filtration', description: 'F7 class filters for superior air quality' },
      { title: 'Smart Controls', description: 'Integrated BMS connectivity' },
      { title: 'Low Noise Operation', description: 'Sound levels below 45 dB(A)' }
    ],
    publishedAt: new Date()
  },
  {
    name: 'Mitsubishi Electric VRF Outdoor Unit',
    slug: 'mitsubishi-vrf-outdoor-unit',
    description: '<p>Variable Refrigerant Flow outdoor unit with inverter technology for maximum energy efficiency. Suitable for multi-zone commercial applications.</p>',
    shortDescription: 'Energy-efficient VRF outdoor unit with inverter technology for commercial applications.',
    category: 'condensing-unit',
    brandName: 'Mitsubishi Electric',
    model: 'PURY-P200YNW-A',
    price: 8500,
    currency: 'USD',
    capacity: '22.4 kW',
    powerConsumption: '6.8 kW',
    efficiency: 'COP 4.2',
    weight: '185 kg',
    availability: 'in-stock',
    featured: true,
    popular: false,
    specifications: {
      coolingCapacity: { value: '22.4', unit: 'kW', category: 'performance' },
      heatingCapacity: { value: '25.0', unit: 'kW', category: 'performance' },
      refrigerant: { value: 'R410A', unit: '', category: 'technical' },
      powerSupply: { value: '380V/3Ph/50Hz', unit: '', category: 'electrical' },
      operatingRange: { value: '-20 to +43', unit: '°C', category: 'environmental' }
    },
    features: [
      { title: 'Inverter Technology', description: 'Variable speed compressor for energy savings' },
      { title: 'Wide Operating Range', description: 'Operates from -20°C to +43°C' },
      { title: 'R410A Refrigerant', description: 'Environmentally friendly refrigerant' },
      { title: 'Compact Design', description: 'Space-saving outdoor unit design' }
    ],
    publishedAt: new Date()
  },
  {
    name: 'Daikin Heat Recovery Ventilation Unit',
    slug: 'daikin-hrv-unit',
    description: '<p>Energy-efficient heat recovery ventilation unit with high-performance heat exchanger. Ideal for residential and light commercial applications.</p>',
    shortDescription: 'Energy-efficient HRV unit with high-performance heat exchanger for residential use.',
    category: 'heat-recovery-ventilation-unit',
    brandName: 'Daikin',
    model: 'VAM-FA',
    price: 3200,
    currency: 'USD',
    capacity: '800 m³/h',
    powerConsumption: '0.8 kW',
    efficiency: 'Heat Recovery 90%',
    weight: '65 kg',
    availability: 'in-stock',
    featured: false,
    popular: true,
    specifications: {
      airflow: { value: '800', unit: 'm³/h', category: 'performance' },
      heatRecovery: { value: '90', unit: '%', category: 'efficiency' },
      filterType: { value: 'G4', unit: '', category: 'filtration' },
      frostProtection: { value: 'Yes', unit: '', category: 'features' },
      bypassDamper: { value: 'Automatic', unit: '', category: 'features' }
    },
    features: [
      { title: '90% Heat Recovery', description: 'High-efficiency heat recovery performance' },
      { title: 'Frost Protection', description: 'Automatic frost protection system' },
      { title: 'Bypass Function', description: 'Summer bypass for free cooling' },
      { title: 'Low Maintenance', description: 'Easy filter access and replacement' }
    ],
    publishedAt: new Date()
  },
  {
    name: 'Carrier Fan Coil Unit 4-Pipe',
    slug: 'carrier-fan-coil-4pipe',
    description: '<p>High-performance 4-pipe fan coil unit for simultaneous heating and cooling. Features quiet operation and precise temperature control.</p>',
    shortDescription: '4-pipe fan coil unit with simultaneous heating/cooling and quiet operation.',
    category: 'fan-coil-unit',
    brandName: 'Carrier',
    model: 'FCU-42N',
    price: 1200,
    currency: 'USD',
    capacity: '3.5 kW',
    powerConsumption: '0.15 kW',
    efficiency: 'High Efficiency',
    weight: '28 kg',
    availability: 'in-stock',
    featured: false,
    popular: true,
    specifications: {
      coolingCapacity: { value: '3.5', unit: 'kW', category: 'performance' },
      heatingCapacity: { value: '4.2', unit: 'kW', category: 'performance' },
      airflow: { value: '450', unit: 'm³/h', category: 'performance' },
      soundLevel: { value: '32', unit: 'dB(A)', category: 'acoustic' },
      waterConnection: { value: '1/2"', unit: '', category: 'technical' }
    },
    features: [
      { title: 'Quiet Operation', description: 'Sound levels as low as 32 dB(A)' },
      { title: '4-Pipe System', description: 'Simultaneous heating and cooling capability' },
      { title: 'Precise Control', description: 'Accurate temperature and humidity control' },
      { title: 'Compact Design', description: 'Space-saving ceiling or wall mount' }
    ],
    publishedAt: new Date()
  },
  {
    name: 'Trane Water Source Heat Pump',
    slug: 'trane-water-source-heat-pump',
    description: '<p>Efficient water source heat pump for commercial applications. Provides both heating and cooling with high COP performance.</p>',
    shortDescription: 'Efficient water source heat pump for commercial heating and cooling applications.',
    category: 'water-source-heat-pump',
    brandName: 'Trane',
    model: 'GWS-020',
    price: 4500,
    currency: 'USD',
    capacity: '7.0 kW',
    powerConsumption: '2.1 kW',
    efficiency: 'COP 4.5',
    weight: '95 kg',
    availability: 'pre-order',
    featured: true,
    popular: false,
    specifications: {
      coolingCapacity: { value: '7.0', unit: 'kW', category: 'performance' },
      heatingCapacity: { value: '8.5', unit: 'kW', category: 'performance' },
      waterFlow: { value: '1.2', unit: 'm³/h', category: 'performance' },
      refrigerant: { value: 'R410A', unit: '', category: 'technical' },
      operatingRange: { value: '10-40', unit: '°C', category: 'environmental' }
    },
    features: [
      { title: 'High COP Performance', description: 'COP up to 4.5 for energy efficiency' },
      { title: 'Reversible Operation', description: 'Heating and cooling in one unit' },
      { title: 'Water Source Efficiency', description: 'Stable water temperature for consistent performance' },
      { title: 'Compact Footprint', description: 'Space-efficient design for commercial use' }
    ],
    publishedAt: new Date()
  },
  {
    name: 'York Exhaust Fan Unit Industrial',
    slug: 'york-exhaust-fan-industrial',
    description: '<p>Heavy-duty industrial exhaust fan unit for commercial and industrial ventilation. Features corrosion-resistant construction and high airflow capacity.</p>',
    shortDescription: 'Heavy-duty industrial exhaust fan with corrosion-resistant construction.',
    category: 'exhaust-unit',
    brandName: 'York',
    model: 'EXH-500',
    price: 2800,
    currency: 'USD',
    capacity: '5000 m³/h',
    powerConsumption: '3.0 kW',
    efficiency: 'High Efficiency Motor',
    weight: '120 kg',
    availability: 'in-stock',
    featured: false,
    popular: false,
    specifications: {
      airflow: { value: '5000', unit: 'm³/h', category: 'performance' },
      staticPressure: { value: '500', unit: 'Pa', category: 'performance' },
      motorType: { value: 'IE3', unit: '', category: 'electrical' },
      protection: { value: 'IP55', unit: '', category: 'environmental' },
      material: { value: 'Galvanized Steel', unit: '', category: 'construction' }
    },
    features: [
      { title: 'Corrosion Resistant', description: 'Galvanized steel construction for durability' },
      { title: 'High Efficiency Motor', description: 'IE3 efficiency class motor' },
      { title: 'Weather Protection', description: 'IP55 protection rating' },
      { title: 'Industrial Grade', description: 'Designed for continuous operation' }
    ],
    publishedAt: new Date()
  }
];

module.exports = {
  async seedProducts() {
    try {
      console.log('Starting product seeding...');
      
      // Check if products already exist
      const existingProducts = await strapi.entityService.findMany('api::product.product');
      
      if (existingProducts.length > 0) {
        console.log(`Found ${existingProducts.length} existing products. Skipping seed.`);
        return { success: true, message: 'Products already exist', count: existingProducts.length };
      }
      
      const createdProducts = [];
      
      for (const productData of sampleProducts) {
        try {
          const product = await strapi.entityService.create('api::product.product', {
            data: productData
          });
          
          createdProducts.push(product);
          console.log(`Created product: ${productData.name}`);
        } catch (error) {
          console.error(`Error creating product ${productData.name}:`, error.message);
        }
      }
      
      console.log(`Successfully seeded ${createdProducts.length} products`);
      
      return {
        success: true,
        message: `Successfully seeded ${createdProducts.length} products`,
        count: createdProducts.length,
        products: createdProducts
      };
      
    } catch (error) {
      console.error('Error seeding products:', error);
      throw error;
    }
  },
  
  async clearProducts() {
    try {
      console.log('Clearing all products...');
      
      const products = await strapi.entityService.findMany('api::product.product');
      
      for (const product of products) {
        await strapi.entityService.delete('api::product.product', product.id);
      }
      
      console.log(`Cleared ${products.length} products`);
      
      return {
        success: true,
        message: `Cleared ${products.length} products`,
        count: products.length
      };
      
    } catch (error) {
      console.error('Error clearing products:', error);
      throw error;
    }
  }
};
