'use strict';

/**
 * quote-request controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::quote-request.quote-request', ({ strapi }) => ({
  async create(ctx) {
    // Add timestamp and source information
    const { data } = ctx.request.body;
    
    // Validate required fields
    if (!data.name || !data.email) {
      return ctx.badRequest('Name and email are required');
    }
    
    // Add metadata
    data.source = 'website';
    data.status = 'new';
    data.priority = 'medium';
    
    // Create the quote request
    const response = await super.create(ctx);
    
    // TODO: Send notification email to admin
    // TODO: Send confirmation email to customer
    
    return response;
  },
  
  async find(ctx) {
    // Only allow authenticated users to view quote requests
    if (!ctx.state.user) {
      return ctx.unauthorized('You must be authenticated to view quote requests');
    }
    
    return super.find(ctx);
  },
  
  async findOne(ctx) {
    // Only allow authenticated users to view quote requests
    if (!ctx.state.user) {
      return ctx.unauthorized('You must be authenticated to view quote requests');
    }
    
    return super.findOne(ctx);
  }
}));
