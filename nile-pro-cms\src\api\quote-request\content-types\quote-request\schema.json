{"kind": "collectionType", "collectionName": "quote_requests", "info": {"singularName": "quote-request", "pluralName": "quote-requests", "displayName": "Quote Request", "description": "Customer quote requests for products"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255}, "email": {"type": "email", "required": true}, "phone": {"type": "string", "maxLength": 50}, "company": {"type": "string", "maxLength": 255}, "message": {"type": "text"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product"}, "productName": {"type": "string", "maxLength": 255}, "status": {"type": "enumeration", "enum": ["new", "contacted", "quoted", "closed"], "default": "new"}, "priority": {"type": "enumeration", "enum": ["low", "medium", "high", "urgent"], "default": "medium"}, "notes": {"type": "text"}, "followUpDate": {"type": "date"}, "quotedPrice": {"type": "decimal", "min": 0}, "quoteCurrency": {"type": "string", "default": "USD", "maxLength": 3}, "source": {"type": "string", "default": "website", "maxLength": 50}}}