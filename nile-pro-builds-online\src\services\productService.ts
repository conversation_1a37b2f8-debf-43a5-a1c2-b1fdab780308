/**
 * Product Service for Strapi CMS integration
 * Handles all product-related API operations
 */

import api from './api';

export interface StrapiProduct {
  id: number;
  attributes: {
    name: string;
    slug: string;
    description: string;
    shortDescription?: string;
    category: string;
    brand?: string;
    model?: string;
    price?: number;
    currency?: string;
    specifications?: any;
    features?: any;
    technicalData?: any;
    dimensions?: any;
    weight?: string;
    powerConsumption?: string;
    capacity?: string;
    efficiency?: string;
    availability: string;
    tags?: any;
    featured: boolean;
    popular: boolean;
    metaTitle?: string;
    metaDescription?: string;
    images?: {
      data: Array<{
        id: number;
        attributes: {
          name: string;
          url: string;
          alternativeText?: string;
          caption?: string;
          width: number;
          height: number;
        };
      }>;
    };
    brochure?: {
      data?: {
        id: number;
        attributes: {
          name: string;
          url: string;
        };
      };
    };
    manuals?: {
      data: Array<{
        id: number;
        attributes: {
          name: string;
          url: string;
        };
      }>;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
}

export interface ProductFilters {
  category?: string;
  brand?: string;
  featured?: boolean;
  popular?: boolean;
  search?: string;
  availability?: string;
  priceMin?: number;
  priceMax?: number;
}

export interface ProductQueryParams extends ProductFilters {
  sort?: string;
  order?: 'asc' | 'desc';
  limit?: number;
  start?: number;
}

export interface QuoteRequest {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message?: string;
  productId: number;
  productName: string;
}

class ProductService {
  /**
   * Get products with filtering, sorting, and pagination
   */
  async getProducts(params: ProductQueryParams = {}): Promise<{
    data: StrapiProduct[];
    meta: {
      pagination: {
        page: number;
        pageSize: number;
        pageCount: number;
        total: number;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    // Population for related data
    queryParams.append('populate[0]', 'images');
    queryParams.append('populate[1]', 'brochure');
    queryParams.append('populate[2]', 'manuals');
    
    // Filtering
    if (params.category) {
      queryParams.append('filters[category][$eq]', params.category);
    }
    if (params.brand) {
      queryParams.append('filters[brand][$containsi]', params.brand);
    }
    if (params.featured !== undefined) {
      queryParams.append('filters[featured][$eq]', params.featured.toString());
    }
    if (params.popular !== undefined) {
      queryParams.append('filters[popular][$eq]', params.popular.toString());
    }
    if (params.availability) {
      queryParams.append('filters[availability][$eq]', params.availability);
    }
    if (params.search) {
      queryParams.append('filters[$or][0][name][$containsi]', params.search);
      queryParams.append('filters[$or][1][description][$containsi]', params.search);
      queryParams.append('filters[$or][2][shortDescription][$containsi]', params.search);
    }
    if (params.priceMin !== undefined) {
      queryParams.append('filters[price][$gte]', params.priceMin.toString());
    }
    if (params.priceMax !== undefined) {
      queryParams.append('filters[price][$lte]', params.priceMax.toString());
    }
    
    // Sorting
    const sortField = params.sort || 'name';
    const sortOrder = params.order || 'asc';
    queryParams.append('sort', `${sortField}:${sortOrder}`);
    
    // Pagination
    if (params.limit) {
      queryParams.append('pagination[pageSize]', params.limit.toString());
    }
    if (params.start) {
      const page = Math.floor(params.start / (params.limit || 25)) + 1;
      queryParams.append('pagination[page]', page.toString());
    }
    
    const response = await api.get(`/products?${queryParams.toString()}`);
    return response.data;
  }

  /**
   * Get a single product by slug
   */
  async getProductBySlug(slug: string): Promise<StrapiProduct | null> {
    const queryParams = new URLSearchParams();
    queryParams.append('filters[slug][$eq]', slug);
    queryParams.append('populate[0]', 'images');
    queryParams.append('populate[1]', 'brochure');
    queryParams.append('populate[2]', 'manuals');
    
    const response = await api.get(`/products?${queryParams.toString()}`);
    return response.data.data[0] || null;
  }

  /**
   * Get a single product by ID
   */
  async getProductById(id: number): Promise<StrapiProduct> {
    const queryParams = new URLSearchParams();
    queryParams.append('populate[0]', 'images');
    queryParams.append('populate[1]', 'brochure');
    queryParams.append('populate[2]', 'manuals');
    
    const response = await api.get(`/products/${id}?${queryParams.toString()}`);
    return response.data.data;
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit: number = 6): Promise<StrapiProduct[]> {
    const result = await this.getProducts({
      featured: true,
      limit,
      sort: 'updatedAt',
      order: 'desc'
    });
    return result.data;
  }

  /**
   * Get popular products
   */
  async getPopularProducts(limit: number = 6): Promise<StrapiProduct[]> {
    const result = await this.getProducts({
      popular: true,
      limit,
      sort: 'updatedAt',
      order: 'desc'
    });
    return result.data;
  }

  /**
   * Get related products based on category and brand
   */
  async getRelatedProducts(productId: number, category: string, brand?: string, limit: number = 4): Promise<StrapiProduct[]> {
    const params: ProductQueryParams = {
      category,
      limit: limit + 1, // Get one extra to exclude current product
      sort: 'updatedAt',
      order: 'desc'
    };
    
    if (brand) {
      params.brand = brand;
    }
    
    const result = await this.getProducts(params);
    // Filter out the current product
    return result.data.filter(product => product.id !== productId).slice(0, limit);
  }

  /**
   * Submit quote request
   */
  async submitQuoteRequest(quoteData: QuoteRequest): Promise<{ success: boolean; message: string }> {
    try {
      await api.post('/quote-requests', { data: quoteData });
      return { success: true, message: 'Quote request submitted successfully' };
    } catch (error) {
      console.error('Error submitting quote request:', error);
      return { success: false, message: 'Failed to submit quote request' };
    }
  }

  /**
   * Get product categories
   */
  async getCategories(): Promise<string[]> {
    // Since categories are stored as enums in the schema, we'll return the predefined list
    return [
      'air-handling-unit',
      'condensing-unit',
      'heat-recovery-ventilation-unit',
      'energy-recovery-ventilation-unit',
      'fan-coil-unit',
      'ecology-unit',
      'water-source-heat-pump',
      'exhaust-unit'
    ];
  }

  /**
   * Get unique brands from products
   */
  async getBrands(): Promise<string[]> {
    try {
      const response = await api.get('/products?fields[0]=brand');
      const products = response.data.data as StrapiProduct[];
      const brands = [...new Set(products.map(p => p.attributes.brand).filter(Boolean))];
      return brands.sort();
    } catch (error) {
      console.error('Error fetching brands:', error);
      return [];
    }
  }
}

export const productService = new ProductService();
