import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  MapPin,
  Calendar,
  Users,
  Award,
  Star,
  ExternalLink,
  Search,
  Filter,
  Grid,
  List,
  Eye,
  Download,
  Phone,
  Mail,
  CheckCircle,
  TrendingUp,
  Clock
} from 'lucide-react';
import { useState, useMemo } from 'react';

const References = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const projects = [
    {
      id: 1,
      title: "Grand Plaza Hotel",
      location: "Cairo, Egypt",
      category: "Hospitality",
      year: "2023",
      description: "Complete MEP installation for 300-room luxury hotel including HVAC, electrical, plumbing, and fire safety systems.",
      detailedDescription: "This prestigious project involved the complete MEP installation for a 300-room luxury hotel in the heart of Cairo. Our team delivered state-of-the-art HVAC systems, advanced electrical infrastructure, comprehensive plumbing solutions, and integrated fire safety systems. The project showcased our expertise in hospitality MEP solutions with a focus on guest comfort and energy efficiency.",
      scope: "Full MEP Installation",
      value: "$2.5M",
      duration: "18 months",
      client: "Grand Plaza Hotels Group",
      image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Central HVAC System", "Guest Room Controls", "Kitchen Ventilation", "Pool Systems"],
      challenges: ["Complex coordination with interior design", "Tight construction schedule", "High-end finish requirements"],
      solutions: ["Advanced BIM modeling for coordination", "Prefabrication to accelerate installation", "Custom fabrication for aesthetic integration"],
      results: ["30% energy savings achieved", "99.9% system uptime", "Enhanced guest satisfaction scores", "LEED Gold certification"],
      technologies: ["Smart Building Automation", "Energy Recovery Systems", "Variable Refrigerant Flow", "IoT Monitoring"],
      certifications: ["LEED Gold", "ASHRAE Compliance", "Local Building Codes"]
    },
    {
      id: 2,
      title: "Cairo Medical Center",
      location: "New Cairo, Egypt",
      category: "Healthcare",
      year: "2023",
      description: "Specialized MEP systems for 200-bed hospital including medical gas, clean rooms, and emergency power systems.",
      detailedDescription: "A critical healthcare project involving the design and installation of specialized MEP systems for a 200-bed medical center. The project required strict adherence to healthcare standards and regulations, with systems designed for reliability, redundancy, and patient safety.",
      scope: "Medical MEP Systems",
      value: "$3.2M",
      duration: "24 months",
      client: "Cairo Healthcare Group",
      image: "https://images.unsplash.com/photo-**********-a9333d879b1f?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-**********-a9333d879b1f?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Medical Gas Systems", "Operating Room HVAC", "Emergency Power", "Infection Control"],
      challenges: ["Strict regulatory compliance", "Zero downtime requirements", "Sterile environment maintenance"],
      solutions: ["Redundant system design", "Phased installation approach", "Advanced filtration systems"],
      results: ["100% regulatory compliance", "Zero system failures", "Improved patient outcomes", "24/7 reliable operation"],
      technologies: ["Medical Gas Distribution", "HEPA Filtration", "UPS Systems", "Building Management"],
      certifications: ["Healthcare Standards", "Medical Gas Compliance", "Emergency Systems Certified"]
    },
    {
      id: 3,
      title: "Pharma Manufacturing Plant",
      location: "6th of October City, Egypt",
      category: "Pharmaceutical",
      year: "2022",
      description: "Clean room MEP systems for pharmaceutical manufacturing facility with strict environmental controls.",
      detailedDescription: "State-of-the-art pharmaceutical manufacturing facility requiring precision environmental controls and regulatory compliance. Our team delivered clean room HVAC systems, process cooling, and compressed air systems meeting international pharmaceutical standards.",
      scope: "Clean Room Systems",
      value: "$4.1M",
      duration: "20 months",
      client: "Pharma Industries Ltd",
      image: "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1587854692152-cbe660dbde88?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Clean Room HVAC", "Process Cooling", "Compressed Air", "Validation Support"],
      challenges: ["ISO 14644 compliance", "Contamination prevention", "Process validation"],
      solutions: ["Precision environmental control", "Validated systems design", "Comprehensive documentation"],
      results: ["FDA compliance achieved", "Zero contamination incidents", "Reduced production costs", "Validated operations"],
      technologies: ["Clean Room Technology", "Process Automation", "Environmental Monitoring", "Validation Systems"],
      certifications: ["ISO 14644", "FDA Compliance", "GMP Standards"]
    },
    {
      id: 4,
      title: "Business Tower Complex",
      location: "New Administrative Capital, Egypt",
      category: "Commercial",
      year: "2022",
      description: "Smart building MEP systems for 40-story office tower with advanced automation and energy management.",
      detailedDescription: "A landmark commercial project featuring a 40-story office tower with cutting-edge smart building technologies. The project integrated advanced building automation, energy management systems, and intelligent lighting controls to create a modern, efficient workspace.",
      scope: "Smart Building MEP",
      value: "$5.8M",
      duration: "30 months",
      client: "Capital Development Corp",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Building Automation", "Energy Management", "Smart Lighting", "Security Integration"],
      challenges: ["High-rise complexity", "Energy efficiency targets", "System integration"],
      solutions: ["Advanced BMS implementation", "Energy optimization algorithms", "Integrated control systems"],
      results: ["40% energy reduction", "LEED Platinum certification", "Improved tenant satisfaction", "Reduced operational costs"],
      technologies: ["IoT Integration", "AI-Powered Controls", "Energy Analytics", "Smart Sensors"],
      certifications: ["LEED Platinum", "Smart Building Certified", "Energy Star"]
    },
    {
      id: 5,
      title: "Food Processing Facility",
      location: "Alexandria, Egypt",
      category: "Food & Beverage",
      year: "2021",
      description: "Specialized MEP systems for food processing plant including cold storage and hygiene systems.",
      detailedDescription: "Large-scale food processing facility requiring specialized MEP systems to maintain strict hygiene standards and precise environmental controls. The project included cold storage systems, process ventilation, and comprehensive waste water treatment.",
      scope: "Food Grade Systems",
      value: "$1.8M",
      duration: "15 months",
      client: "Alexandria Food Industries",
      image: "https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Cold Storage Systems", "Process Ventilation", "Hygiene Systems", "Waste Water Treatment"],
      challenges: ["Food safety compliance", "Temperature control precision", "Hygiene maintenance"],
      solutions: ["Food-grade materials", "Precision refrigeration", "Automated cleaning systems"],
      results: ["Zero food safety incidents", "30% energy savings", "HACCP compliance", "Improved production efficiency"],
      technologies: ["Ammonia Refrigeration", "CIP Systems", "Process Automation", "Environmental Monitoring"],
      certifications: ["HACCP", "Food Safety Standards", "Environmental Compliance"]
    },
    {
      id: 6,
      title: "Shopping Mall Complex",
      location: "Giza, Egypt",
      category: "Retail",
      year: "2021",
      description: "Complete MEP installation for large shopping center including retail spaces, restaurants, and entertainment areas.",
      detailedDescription: "Comprehensive MEP installation for a major shopping center serving over 200 retail outlets, restaurants, and entertainment facilities. The project required diverse MEP solutions to accommodate various tenant requirements while maintaining overall system efficiency.",
      scope: "Retail MEP Systems",
      value: "$3.7M",
      duration: "22 months",
      client: "Giza Mall Development",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1555636222-cae831e670b3?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1519567241046-7f570eee3ce6?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Retail HVAC", "Escalator Systems", "Fire Safety", "Parking Ventilation"],
      challenges: ["Diverse tenant requirements", "High foot traffic areas", "Complex fire safety"],
      solutions: ["Flexible HVAC zones", "Advanced fire suppression", "Efficient ventilation design"],
      results: ["Optimal shopping comfort", "25% energy savings", "Enhanced safety systems", "Improved air quality"],
      technologies: ["Variable Air Volume", "Fire Detection Systems", "Parking Ventilation", "Energy Management"],
      certifications: ["Fire Safety Compliance", "Building Codes", "Energy Efficiency"]
    },
    {
      id: 7,
      title: "Sophia Resident and Commercial Compound",
      location: "Cairo, Egypt",
      category: "Residential",
      year: "2023",
      description: "HVAC, Fire Fighting, and Plumbing works for residential and commercial compound including basement systems and building risers.",
      detailedDescription: "Comprehensive MEP installation for Sophia Resident and Commercial Compound, featuring complete HVAC systems in basement buildings, fire fighting works in basement risers, and plumbing network installation throughout basement buildings and risers. This project demonstrates our expertise in mixed-use developments.",
      scope: "HVAC, Fire Fighting & Plumbing",
      value: "$1.2M",
      duration: "12 months",
      client: "El Shorouk Construction",
      image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["HVAC Works in Basement Buildings", "Fire Fighting Works in Basement Risers", "Plumbing Network in Basement Buildings", "Building Risers Installation"],
      challenges: ["Complex basement coordination", "Mixed-use requirements", "Riser system integration"],
      solutions: ["Systematic basement installation", "Integrated riser design", "Coordinated MEP systems"],
      results: ["Efficient basement climate control", "Comprehensive fire safety coverage", "Reliable plumbing distribution", "Seamless riser integration"],
      technologies: ["HVAC Distribution", "Fire Suppression Systems", "Plumbing Networks", "Riser Systems"],
      certifications: ["Fire Safety Standards", "Building Codes", "MEP Compliance"]
    },
    {
      id: 8,
      title: "Cairo Gate Project",
      location: "Cairo, Egypt",
      category: "Residential",
      year: "2022",
      description: "Drainage network, water supply networks, and landscape works for 25 villas in prestigious residential development.",
      detailedDescription: "Extensive infrastructure project for Cairo Gate residential development, encompassing complete drainage networks, water supply systems, and landscape works for 25 luxury villas. This project showcased our capability in residential infrastructure and landscape integration.",
      scope: "Infrastructure & Landscape",
      value: "$800K",
      duration: "10 months",
      client: "El Shorouk Construction",
      image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Drainage Network in Villas", "Water Supply Networks in Villas", "Landscape Works", "25 Villas Awarded"],
      challenges: ["Multiple villa coordination", "Landscape integration", "Infrastructure timing"],
      solutions: ["Phased installation approach", "Integrated landscape design", "Coordinated utility installation"],
      results: ["Efficient drainage systems", "Reliable water supply", "Beautiful landscape integration", "25 successful villa completions"],
      technologies: ["Drainage Systems", "Water Distribution", "Landscape Irrigation", "Utility Networks"],
      certifications: ["Water Supply Standards", "Drainage Compliance", "Landscape Certification"]
    },
    {
      id: 9,
      title: "TAG City",
      location: "Cairo, Egypt",
      category: "Residential",
      year: "2022",
      description: "Fire fighting and plumbing network installation for residential villas and buildings in TAG City development.",
      detailedDescription: "Comprehensive fire safety and plumbing installation for TAG City residential development. The project involved complete fire fighting systems and plumbing networks for multiple villas and buildings, ensuring safety and comfort for residents.",
      scope: "Fire Fighting & Plumbing",
      value: "$600K",
      duration: "8 months",
      client: "ALFA Company",
      image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Fire Fighting Works in Villas", "Fire Fighting Works in Buildings", "Plumbing Network in Villas", "Plumbing Network in Buildings"],
      challenges: ["Multi-building coordination", "Fire safety compliance", "Residential standards"],
      solutions: ["Systematic installation approach", "Comprehensive fire protection", "Reliable plumbing systems"],
      results: ["Complete fire safety coverage", "Efficient plumbing distribution", "Residential comfort achieved", "Safety standards exceeded"],
      technologies: ["Fire Suppression Systems", "Plumbing Networks", "Safety Systems", "Water Distribution"],
      certifications: ["Fire Safety Compliance", "Plumbing Standards", "Building Codes"]
    },
    {
      id: 10,
      title: "Sarai Compound",
      location: "Cairo, Egypt",
      category: "Residential",
      year: "2021",
      description: "Fire fighting and plumbing network systems for residential buildings in Sarai Compound development.",
      detailedDescription: "Essential MEP services for Sarai Compound residential development, focusing on fire safety systems and plumbing networks. The project ensured comprehensive fire protection and reliable water distribution throughout the residential buildings.",
      scope: "Fire Fighting & Plumbing",
      value: "$450K",
      duration: "6 months",
      client: "ALFA Company",
      image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["Fire Fighting Works in Buildings", "Plumbing Network in Buildings", "Safety Systems", "Water Distribution"],
      challenges: ["Building integration", "Safety compliance", "System reliability"],
      solutions: ["Integrated fire protection", "Reliable plumbing design", "Quality installation"],
      results: ["Comprehensive fire safety", "Reliable water supply", "Building code compliance", "Resident satisfaction"],
      technologies: ["Fire Protection Systems", "Plumbing Infrastructure", "Safety Equipment", "Water Systems"],
      certifications: ["Fire Safety Standards", "Plumbing Compliance", "Building Regulations"]
    },
    {
      id: 11,
      title: "Masbiro Towers",
      location: "Cairo, Egypt",
      category: "Commercial",
      year: "2021",
      description: "HVAC works in basement buildings including duct works installation, fans installation, and ducts painting works.",
      detailedDescription: "Comprehensive HVAC installation for Masbiro Towers basement buildings, featuring complete ductwork systems, fan installations, and professional painting works. This project demonstrated our expertise in commercial HVAC systems and attention to finishing details.",
      scope: "HVAC Systems",
      value: "$750K",
      duration: "9 months",
      client: "Arab Contractor Company",
      image: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop&crop=center",
      gallery: [
        "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=800&h=600&fit=crop&crop=center"
      ],
      features: ["HVAC Works in Basement Buildings", "Duct Works Installation", "Fans Installation", "Ducts Painting Works"],
      challenges: ["Basement space constraints", "Ductwork complexity", "Finishing requirements"],
      solutions: ["Optimized ductwork design", "Professional installation", "Quality finishing works"],
      results: ["Efficient basement ventilation", "Professional ductwork installation", "Quality painted finishes", "Optimal air distribution"],
      technologies: ["HVAC Ductwork", "Ventilation Fans", "Air Distribution", "Painting Systems"],
      certifications: ["HVAC Standards", "Ventilation Compliance", "Quality Finishing"]
    }
  ];

  const categories = ['all', 'Hospitality', 'Healthcare', 'Pharmaceutical', 'Commercial', 'Food & Beverage', 'Retail', 'Residential'];

  const filteredProjects = useMemo(() => {
    let filtered = projects;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.location.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.features.some(feature => feature.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [searchQuery, selectedCategory, projects]);

  const stats = [
    { icon: <Building2 className="h-8 w-8" />, value: "200+", label: "Projects Completed" },
    { icon: <Users className="h-8 w-8" />, value: "150+", label: "Satisfied Clients" },
    { icon: <Award className="h-8 w-8" />, value: "15+", label: "Years Experience" },
    { icon: <Star className="h-8 w-8" />, value: "98%", label: "Client Satisfaction" }
  ];

  const testimonials = [
    {
      id: 1,
      name: "Ahmed Hassan",
      position: "Project Manager",
      company: "Grand Plaza Hotel",
      quote: "Nile Pro delivered exceptional MEP solutions for our hotel. Their attention to detail and professional approach exceeded our expectations. The systems have been running flawlessly since installation.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      projectValue: "$2.5M",
      completionYear: "2023"
    },
    {
      id: 2,
      name: "Dr. Sarah Mohamed",
      position: "Facility Director",
      company: "Cairo Medical Center",
      quote: "The medical MEP systems installed by Nile Pro meet all international standards. Their expertise in healthcare facilities is outstanding, and their support team is always available when needed.",
      rating: 5,
      image: "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face",
      projectValue: "$3.2M",
      completionYear: "2023"
    },
    {
      id: 3,
      name: "Mahmoud Ali",
      position: "Operations Manager",
      company: "Business Tower Complex",
      quote: "The smart building systems implemented by Nile Pro have significantly improved our energy efficiency and operational costs. The ROI has exceeded our projections.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      projectValue: "$5.8M",
      completionYear: "2022"
    },
    {
      id: 4,
      name: "Eng. Fatima Khalil",
      position: "Technical Director",
      company: "Pharma Industries Ltd",
      quote: "Nile Pro's expertise in pharmaceutical MEP systems is unmatched. They delivered a fully validated clean room facility that meets all FDA requirements.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face",
      projectValue: "$4.1M",
      completionYear: "2022"
    },
    {
      id: 5,
      name: "Omar Mansour",
      position: "Facility Manager",
      company: "Alexandria Food Industries",
      quote: "The food-grade MEP systems installed by Nile Pro have helped us maintain the highest hygiene standards while reducing our operational costs significantly.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face",
      projectValue: "$1.8M",
      completionYear: "2021"
    },
    {
      id: 6,
      name: "Layla Abdel Rahman",
      position: "Mall Operations Director",
      company: "Giza Mall Development",
      quote: "Nile Pro's comprehensive MEP solutions have created a comfortable shopping environment for our customers while keeping our energy costs under control.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face",
      projectValue: "$3.7M",
      completionYear: "2021"
    }
  ];

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Our References
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Explore our portfolio of successful MEP projects across various industries.
              Each project showcases our commitment to excellence and innovation.
            </p>

            {/* Search and Filter Controls */}
            <div className="max-w-4xl mx-auto mb-12">
              <div className="flex flex-col lg:flex-row gap-4 mb-6">
                {/* Search Bar */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search projects by name, location, or features..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 min-h-[44px]"
                  />
                </div>

                {/* View Mode Toggle */}
                <div className="flex border border-border rounded-md overflow-hidden">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-none min-h-[44px]"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-none min-h-[44px]"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Category Tabs */}
              <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
                <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 min-h-[44px]">
                  {categories.map((category) => (
                    <TabsTrigger key={category} value={category}>
                      {category === 'all' ? 'All Projects' : category}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-white">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Featured <span className="text-primary">Projects</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              A selection of our most significant MEP projects demonstrating our expertise across different sectors.
            </p>

            {/* Results Summary */}
            <div className="text-center mb-8">
              <p className="text-muted-foreground">
                Showing {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
                {searchQuery && ` for "${searchQuery}"`}
                {selectedCategory !== 'all' && ` in ${selectedCategory}`}
              </p>
            </div>
          </div>

          {/* Projects Display */}
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8"
            : "space-y-8"
          }>
            {filteredProjects.map((project) => (
              <Card key={project.id} className={`group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] border-border ${viewMode === 'list' ? 'flex flex-col lg:flex-row' : ''
                }`}>
                <div className={`overflow-hidden ${viewMode === 'list'
                  ? 'lg:w-1/3 aspect-video lg:aspect-square rounded-l-lg'
                  : 'aspect-video rounded-t-lg'
                  }`}>
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>

                <div className={viewMode === 'list' ? 'lg:w-2/3 flex flex-col' : ''}>
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary">{project.category}</Badge>
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {project.year}
                      </span>
                    </div>
                    <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                      {project.title}
                    </CardTitle>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4 mr-1" />
                      {project.location}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4 flex-1">
                    <p className="text-muted-foreground text-sm">
                      {viewMode === 'list' ? project.detailedDescription : project.description}
                    </p>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.duration}</span>
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.client}</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.scope}</span>
                      </div>
                    </div>

                    {viewMode === 'list' && (
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-foreground text-sm mb-2">Key Results:</h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                            {project.results.slice(0, 4).map((result, idx) => (
                              <div key={idx} className="flex items-start text-xs text-muted-foreground">
                                <CheckCircle className="h-3 w-3 text-primary mr-1 mt-0.5 flex-shrink-0" />
                                {result}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="space-y-2">
                      <h4 className="font-semibold text-foreground text-sm">Key Features:</h4>
                      <div className="flex flex-wrap gap-1">
                        {project.features.map((feature, featureIndex) => (
                          <Badge key={featureIndex} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button
                        variant="outline"
                        className="flex-1 group border-primary text-primary hover:bg-primary hover:text-white min-h-[44px]"
                        onClick={() => {
                          // Show project details modal or navigate to project detail page
                          console.log('View project details:', project.id);
                          // TODO: Implement project detail modal or page
                        }}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="min-h-[44px]"
                        onClick={() => {
                          // Download project datasheet
                          const link = document.createElement('a');
                          link.href = `/downloads/project-${project.id}-datasheet.pdf`;
                          link.download = `${project.title.replace(/\s+/g, '-')}-Datasheet.pdf`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>

          {/* No Results */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <h3 className="text-lg font-semibold mb-2">No projects found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or category filter.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Enhanced Testimonials */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Client <span className="text-primary">Testimonials</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Hear what our clients say about our MEP solutions and services. Real feedback from real projects.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id} className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4 bg-muted">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex mb-2">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        {testimonial.completionYear}
                      </div>
                    </div>
                  </div>

                  <p className="text-muted-foreground mb-4 italic text-sm leading-relaxed">
                    "{testimonial.quote}"
                  </p>

                  <div className="border-t border-border pt-4">
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.position}</div>
                    <div className="text-sm text-primary font-medium">{testimonial.company}</div>


                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
            Ready to Start Your <span className="text-primary">Next Project?</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Join our growing list of satisfied clients. Contact us today to discuss your MEP requirements
            and discover how we can bring your vision to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-gradient-primary hover:opacity-90 shadow-primary"
              onClick={() => {
                // Navigate to contact page
                window.location.href = '/contact';
              }}
            >
              <Phone className="mr-2 h-5 w-5" />
              Request Consultation
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-primary text-primary hover:bg-primary hover:text-white"
              onClick={() => {
                // Navigate to contact page
                window.location.href = '/contact';
              }}
            >
              <Mail className="mr-2 h-5 w-5" />
              Get Quote
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-primary text-primary hover:bg-primary hover:text-white"
              onClick={() => {
                // Download company portfolio PDF
                const link = document.createElement('a');
                link.href = '/downloads/nile-pro-portfolio.pdf';
                link.download = 'Nile-Pro-MEP-Portfolio.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              <Download className="mr-2 h-5 w-5" />
              Download Portfolio
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-accent text-accent hover:bg-accent hover:text-white"
              onClick={() => {
                // Download references PDF
                const link = document.createElement('a');
                link.href = '/downloads/nile-pro-references.pdf';
                link.download = 'Nile-Pro-MEP-References.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              <Download className="mr-2 h-5 w-5" />
              Download References PDF
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default References;
