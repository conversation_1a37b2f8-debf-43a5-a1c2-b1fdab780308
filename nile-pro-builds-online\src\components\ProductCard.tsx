import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Star,
  Heart,
  Eye,
  Download,
  ExternalLink
} from 'lucide-react';

interface ProductCardProps {
  product: {
    id: string;
    brandId: string;
    name: string;
    model: string;
    category: string;
    image: string;
    description: string;
    specifications: Record<string, string>;
    features: string[];
    availability: string;
    rating: number;
    reviews: number;
    featured: boolean;
  };
  showPrice?: boolean;
  showBrand?: boolean;
  className?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  showPrice = false,
  showBrand = false,
  className = ""
}) => {
  const getBrandName = (brandId: string) => {
    const brandNames: Record<string, string> = {
      'hiref': 'HiRef',
      'dkc': 'DKC',
      'carrier': 'Carrier',
      'daikin': 'Daikin',
      'trane': 'Trane',
      'york': 'York'
    };
    return brandNames[brandId] || brandId;
  };

  const getCategoryName = (category: string) => {
    const categoryNames: Record<string, string> = {
      'air-handling-unit': 'Air Handling Unit',
      'condensing-unit': 'Condensing Unit',
      'heat-recovery-ventilation-unit': 'HRV Unit',
      'energy-recovery-ventilation-unit': 'ERV Unit',
      'fan-coil-unit': 'Fan Coil Unit',
      'ecology-unit': 'Ecology Unit',
      'water-source-heat-pump': 'Heat Pump',
      'exhaust-unit': 'Exhaust Unit'
    };
    return categoryNames[category] || category;
  };

  return (
    <Card className={`border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02] ${className}`}>
      <div className="aspect-video overflow-hidden rounded-t-lg">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder.svg';
          }}
        />
      </div>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            {showBrand && (
              <p className="text-sm text-primary font-medium mb-1">
                {getBrandName(product.brandId)}
              </p>
            )}
            <h3 className="text-lg font-bold text-foreground mb-1 line-clamp-2">
              {product.name}
            </h3>
            <p className="text-sm text-muted-foreground mb-2">
              Model: {product.model}
            </p>
          </div>
          <Badge
            variant={product.availability === 'in-stock' ? 'default' : 'secondary'}
            className="ml-2"
          >
            {product.availability === 'in-stock' ? 'In Stock' :
              product.availability === 'pre-order' ? 'Pre-Order' : 'Out of Stock'}
          </Badge>
        </div>

        <Badge variant="outline" className="mb-3">
          {getCategoryName(product.category)}
        </Badge>

        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
          {product.description}
        </p>

        {/* Key Specifications */}
        <div className="mb-4">
          <h4 className="text-xs font-semibold text-foreground mb-2">Key Specs:</h4>
          <div className="space-y-1">
            {Object.entries(product.specifications).slice(0, 2).map(([key, value]) => (
              <div key={key} className="flex justify-between text-xs">
                <span className="text-muted-foreground">{key}:</span>
                <span className="text-foreground font-medium">{value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {product.features.slice(0, 3).map((feature, idx) => (
              <Badge key={idx} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>
        </div>

        {/* Rating */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{product.rating}</span>
            <span className="text-xs text-muted-foreground">
              ({product.reviews} reviews)
            </span>
          </div>
          {product.featured && (
            <Badge variant="secondary" className="text-xs">
              Featured
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button size="sm" className="flex-1" onClick={() => {
            // Navigate to product details page
            window.location.href = `/products/${product.slug}`;
          }}>
            <Eye className="h-3 w-3 mr-1" />
            View Details
          </Button>
          <Button variant="outline" size="sm" onClick={() => {
            // Add to favorites (placeholder)
            console.log('Add to favorites:', product.id);
          }}>
            <Heart className="h-3 w-3" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => {
            // Download product datasheet
            if (product.downloads && product.downloads.length > 0) {
              const link = document.createElement('a');
              link.href = product.downloads[0].url;
              link.download = product.downloads[0].title;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            } else {
              // Fallback to generic product datasheet
              const link = document.createElement('a');
              link.href = `/downloads/product-${product.id}-datasheet.pdf`;
              link.download = `${product.title.replace(/\s+/g, '-')}-Datasheet.pdf`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }
          }}>
            <Download className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
