/**
 * Modern Product Card Component
 * Implements the comprehensive brief specifications with Strapi integration
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Star, Eye, Heart } from 'lucide-react';
import { StrapiProduct } from '@/services/productService';
import { transformStrapiProduct, getAvailabilityStatus, formatPrice } from '@/utils/strapiTransform';

interface ModernProductCardProps {
  product: StrapiProduct;
  showCategory?: boolean;
  showBrand?: boolean;
  showSpecs?: boolean;
  maxSpecs?: number;
  className?: string;
  onImageError?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  onFavorite?: (productId: number) => void;
  isFavorite?: boolean;
}

const ModernProductCard = ({
  product,
  showCategory = true,
  showBrand = true,
  showSpecs = true,
  maxSpecs = 3,
  className = "",
  onImageError,
  onFavorite,
  isFavorite = false
}: ModernProductCardProps) => {
  const transformedProduct = transformStrapiProduct(product);
  const { attributes } = product;
  const mainImage = attributes.images?.data?.[0];
  const availabilityStatus = getAvailabilityStatus(attributes.availability);

  // Get key specifications for display
  const keySpecs = transformedProduct.specifications.slice(0, maxSpecs);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.src = '/placeholder.svg';
    onImageError?.(e);
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavorite?.(product.id);
  };

  return (
    <div className={`group hover:-translate-y-1 transition-all duration-300 ${className}`}>
      <Card className="h-full bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-0">
        <Link to={`/products/${attributes.slug}`} className="block">
          {/* Product Image with Status Badge */}
          <div className="relative aspect-[4/3] overflow-hidden bg-gray-50">
            {mainImage ? (
              <img
                src={mainImage.attributes.url.startsWith('http')
                  ? mainImage.attributes.url
                  : `${import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337'}${mainImage.attributes.url}`}
                alt={mainImage.attributes.alternativeText || attributes.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <div className="text-gray-400 text-center">
                  <Eye className="w-8 h-8 mx-auto mb-2" />
                  <span className="text-sm">No Image</span>
                </div>
              </div>
            )}

            {/* Status Badge */}
            <div className="absolute top-3 right-3">
              <Badge
                className={`${availabilityStatus.bgColor} ${availabilityStatus.color} border-0 text-xs font-medium px-2 py-1`}
              >
                {availabilityStatus.label}
              </Badge>
            </div>

            {/* Featured Badge */}
            {attributes.featured && (
              <div className="absolute top-3 left-3">
                <Badge className="bg-blue-600 text-white border-0 text-xs font-medium px-2 py-1">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              </div>
            )}

            {/* Favorite Button */}
            {onFavorite && (
              <button
                onClick={handleFavoriteClick}
                className="absolute bottom-3 right-3 p-2 bg-white bg-opacity-90 rounded-full shadow-md hover:bg-opacity-100 transition-all duration-200"
              >
                <Heart
                  className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`}
                />
              </button>
            )}

            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300" />
          </div>

          {/* Product Information */}
          <CardContent className="p-5">
            {/* Brand and Category */}
            {(showBrand || showCategory) && (
              <div className="flex items-center justify-between mb-2">
                {showBrand && attributes.brandName && (
                  <span className="text-sm text-blue-600 font-medium">
                    {attributes.brandName}
                  </span>
                )}
                {showCategory && (
                  <span className="text-xs text-gray-500 capitalize">
                    {attributes.category.replace(/-/g, ' ')}
                  </span>
                )}
              </div>
            )}

            {/* Product Title */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {attributes.name}
            </h3>

            {/* Short Description */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {attributes.shortDescription || transformedProduct.shortDescription}
            </p>

            {/* Key Specifications */}
            {showSpecs && keySpecs.length > 0 && (
              <div className="mb-4 space-y-1">
                {keySpecs.map((spec, index) => (
                  <div key={index} className="flex justify-between text-xs text-gray-600">
                    <span className="font-medium">{spec.name}:</span>
                    <span className="text-right">{spec.value} {spec.unit}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Price and Action */}
            <div className="flex items-center justify-between mt-4">
              <div className="flex flex-col">
                {attributes.price ? (
                  <span className="text-lg font-bold text-blue-600">
                    {formatPrice(attributes.price, attributes.currency)}
                  </span>
                ) : (
                  <span className="text-sm text-gray-500 font-medium">Contact for Price</span>
                )}
              </div>

              <Button
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors group"
              >
                View Details
                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </CardContent>
        </Link>
      </Card>
    </div>
  );
};

export default ModernProductCard;
