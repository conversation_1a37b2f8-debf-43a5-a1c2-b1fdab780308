/**
 * Modern Product Detail Page
 * Comprehensive product details page with Strapi integration
 */

import React, { useState } from 'react';
import { useParams, Link, Navigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Star, 
  Share2, 
  Download, 
  FileText, 
  Settings, 
  Info,
  ChevronRight,
  Building2,
  Tag
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { useProduct, useRelatedProducts } from '@/hooks/useProducts';
import { getAvailabilityStatus, formatPrice } from '@/utils/strapiTransform';
import ModernProductImageGallery from '@/components/product/ModernProductImageGallery';
import ModernProductSpecifications from '@/components/product/ModernProductSpecifications';
import QuoteForm from '@/components/product/QuoteForm';
import ModernProductCard from '@/components/product/ModernProductCard';

const ModernProductDetail = () => {
  const { slug } = useParams<{ slug: string }>();
  const [activeTab, setActiveTab] = useState('overview');

  const { data: product, isLoading, error } = useProduct(slug || '');
  const { data: relatedProducts } = useRelatedProducts(
    product?.id || 0,
    product?.attributes.category || '',
    product?.attributes.brandName,
    4
  );

  if (!slug) {
    return <Navigate to="/products" replace />;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-8">
            {/* Breadcrumb skeleton */}
            <div className="h-4 bg-gray-200 rounded w-64" />
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Image skeleton */}
              <div className="aspect-[4/3] bg-gray-200 rounded-lg" />
              
              {/* Content skeleton */}
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-5/6" />
                </div>
                <div className="h-32 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
            <p className="text-gray-600 mb-6">
              The product you're looking for doesn't exist or has been removed.
            </p>
            <Button asChild>
              <Link to="/products">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Products
              </Link>
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const { attributes } = product;
  const availabilityStatus = getAvailabilityStatus(attributes.availability);

  const shareProduct = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: attributes.name,
          text: attributes.shortDescription || 'Check out this product',
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/products">Products</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{attributes.name}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link to="/products">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Products
          </Link>
        </Button>

        {/* Product Header */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product Gallery */}
          <div>
            <ModernProductImageGallery product={product} />
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            {/* Brand and Category */}
            <div className="flex items-center gap-3">
              {attributes.brandName && (
                <Badge variant="secondary" className="text-blue-600 bg-blue-50">
                  <Building2 className="w-3 h-3 mr-1" />
                  {attributes.brandName}
                </Badge>
              )}
              <Badge variant="outline">
                <Tag className="w-3 h-3 mr-1" />
                {attributes.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Badge>
              <Badge className={`${availabilityStatus.bgColor} ${availabilityStatus.color} border-0`}>
                {availabilityStatus.label}
              </Badge>
            </div>

            {/* Product Title */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {attributes.name}
              </h1>
              {attributes.model && (
                <p className="text-lg text-gray-600">Model: {attributes.model}</p>
              )}
            </div>

            {/* Price */}
            {attributes.price && (
              <div className="text-2xl font-bold text-blue-600">
                {formatPrice(attributes.price, attributes.currency)}
              </div>
            )}

            {/* Description */}
            <div className="prose prose-gray max-w-none">
              <div dangerouslySetInnerHTML={{ __html: attributes.description }} />
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button size="lg" className="flex-1 bg-blue-600 hover:bg-blue-700">
                Request Quote
              </Button>
              <Button variant="outline" size="lg" onClick={shareProduct}>
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              {attributes.featured && (
                <Button variant="outline" size="lg">
                  <Star className="w-4 h-4 mr-2" />
                  Featured
                </Button>
              )}
            </div>

            {/* Quick Specs */}
            {(attributes.capacity || attributes.powerConsumption || attributes.efficiency) && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Key Specifications</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  {attributes.capacity && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Capacity:</span>
                      <span className="font-medium">{attributes.capacity}</span>
                    </div>
                  )}
                  {attributes.powerConsumption && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Power:</span>
                      <span className="font-medium">{attributes.powerConsumption}</span>
                    </div>
                  )}
                  {attributes.efficiency && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Efficiency:</span>
                      <span className="font-medium">{attributes.efficiency}</span>
                    </div>
                  )}
                  {attributes.weight && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Weight:</span>
                      <span className="font-medium">{attributes.weight}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tabbed Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Info className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="specifications" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Specifications
            </TabsTrigger>
            <TabsTrigger value="downloads" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Downloads
            </TabsTrigger>
            <TabsTrigger value="quote" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Get Quote
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Product Overview</h2>
              <div className="prose prose-gray max-w-none">
                <div dangerouslySetInnerHTML={{ __html: attributes.description }} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="specifications">
            <div className="bg-white rounded-lg p-6">
              <ModernProductSpecifications product={product} />
            </div>
          </TabsContent>

          <TabsContent value="downloads" className="space-y-6">
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Downloads & Documentation</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {attributes.brochure?.data && (
                  <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center gap-3">
                      <FileText className="w-8 h-8 text-blue-600" />
                      <div className="flex-1">
                        <h3 className="font-medium">Product Brochure</h3>
                        <p className="text-sm text-gray-600">Detailed product information</p>
                      </div>
                      <Button size="sm" asChild>
                        <a 
                          href={attributes.brochure.data.attributes.url.startsWith('http') 
                            ? attributes.brochure.data.attributes.url 
                            : `${import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337'}${attributes.brochure.data.attributes.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Download className="w-4 h-4" />
                        </a>
                      </Button>
                    </div>
                  </div>
                )}
                
                {attributes.manuals?.data?.map((manual, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center gap-3">
                      <FileText className="w-8 h-8 text-green-600" />
                      <div className="flex-1">
                        <h3 className="font-medium">{manual.attributes.name}</h3>
                        <p className="text-sm text-gray-600">Installation & operation manual</p>
                      </div>
                      <Button size="sm" asChild>
                        <a 
                          href={manual.attributes.url.startsWith('http') 
                            ? manual.attributes.url 
                            : `${import.meta.env.VITE_STRAPI_URL || 'http://localhost:1337'}${manual.attributes.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Download className="w-4 h-4" />
                        </a>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="quote">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <QuoteForm product={product} />
              <div className="bg-white rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Why Choose Nile Pro MEP?</h3>
                <ul className="space-y-3 text-sm">
                  <li className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                    <span>Expert technical consultation and support</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                    <span>Competitive pricing and flexible payment terms</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                    <span>Fast delivery and professional installation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                    <span>Comprehensive warranty and after-sales service</span>
                  </li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Related Products */}
        {relatedProducts && relatedProducts.length > 0 && (
          <div className="mt-16">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Related Products</h2>
              <Button variant="outline" asChild>
                <Link to="/products">
                  View All Products
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ModernProductCard
                  key={relatedProduct.id}
                  product={relatedProduct}
                  showSpecs={false}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default ModernProductDetail;
