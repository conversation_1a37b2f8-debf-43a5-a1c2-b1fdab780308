import { createMotionComponent } from '../../../motion/index.mjs';
import { createDomVisualElement } from '../../dom/create-visual-element.mjs';
import { featureBundle } from './feature-bundle.mjs';

function createMotionComponentWithFeatures(Component, options) {
    return createMotionComponent(Component, options, featureBundle, createDomVisualElement);
}

export { createMotionComponentWithFeatures };
