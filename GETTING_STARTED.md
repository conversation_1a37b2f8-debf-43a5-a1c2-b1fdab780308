# Getting Started with Modern Product Card and Product Details Pages

## 🚀 Quick Start Guide

The modern Product Card and Product Details Pages have been successfully implemented with Strapi CMS integration. Follow these steps to get started:

### 1. Start the Strapi CMS Backend

```bash
cd nile-pro-cms
npm install
npm run develop
```

The Strapi admin panel will be available at: http://localhost:1337/admin

### 1.1. Set Up API Permissions (IMPORTANT!)

After Strapi starts, you need to enable public access to the API endpoints:

1. **Open Strapi Admin Panel**: http://localhost:1337/admin
2. **Go to Settings** > **Users & Permissions Plugin** > **Roles**
3. **Click on "Public" role**
4. **In the Permissions section**, find **"Product"** and enable:
   - ✅ `find`
   - ✅ `findOne`
5. **Find "Brand"** and enable:
   - ✅ `find`
   - ✅ `findOne`
6. **Find "Quote-request"** and enable:
   - ✅ `create`
7. **Click "Save" button**

**⚠️ Without these permissions, you'll get 404 errors on API calls!**

### 2. Start the React Frontend

```bash
cd nile-pro-builds-online
npm install
npm run dev
```

The frontend will be available at: http://localhost:5173

### 3. Seed Sample Products (First Time Setup)

1. Navigate to the Admin page: http://localhost:5173/admin
2. In the "Database Management" section, click **"Seed Products"**
3. This will create 6 sample products with different categories and brands

### 4. View the Modern Components

- **Products Page**: http://localhost:5173/products
- **Product Details**: Click on any product card to view details
- **Homepage**: Features section now shows featured products from Strapi

## 🎯 Key Features Implemented

### ✅ Modern Product Card
- Professional design with Nile Pro branding
- Hover effects and smooth animations
- Status badges (In Stock, Featured, etc.)
- Key specifications display
- Responsive design for all devices

### ✅ Product Details Page
- Interactive image gallery with zoom and fullscreen
- Tabbed content organization (Overview, Specifications, Downloads, Quote)
- Professional breadcrumb navigation
- Related products section
- Quote request form

### ✅ Advanced Search & Filtering
- Real-time search with debouncing
- Category and brand filtering
- Sort by multiple criteria (name, price, category, brand, featured)
- Grid/List view toggle
- Featured products section

### ✅ Strapi CMS Integration
- Dynamic content management
- Product and brand relationships
- Quote request handling
- Image optimization and management

## 🔧 Configuration

### Environment Variables

The following environment variables are already configured in `.env`:

```env
# Strapi CMS Configuration
VITE_USE_STRAPI=true
VITE_STRAPI_URL=http://localhost:1337
VITE_STRAPI_API_TOKEN=
VITE_STRAPI_FALLBACK=true
```

### Strapi Content Types

The following content types are configured:

1. **Product** - Main product information with specifications, features, and media
2. **Brand** - Brand information and relationships
3. **Quote Request** - Customer quote requests with status tracking

## 📱 Component Usage

### Using Modern Product Card

```tsx
import { ModernProductCard } from '@/components/product';
import { useFeaturedProducts } from '@/hooks/useProducts';

const MyComponent = () => {
  const { data: products } = useFeaturedProducts(6);
  
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {products?.map((product) => (
        <ModernProductCard
          key={product.id}
          product={product}
          showSpecs={true}
          maxSpecs={3}
        />
      ))}
    </div>
  );
};
```

### Using Product Grid with Filtering

```tsx
import { ProductGrid } from '@/components/product';
import { useProducts } from '@/hooks/useProducts';

const ProductsPage = () => {
  const { data: productsResponse, isLoading, error } = useProducts({
    category: 'air-handling-unit',
    featured: true,
    limit: 12
  });
  
  return (
    <ProductGrid
      products={productsResponse?.data || []}
      loading={isLoading}
      error={error?.message}
      showFilters={true}
      showSorting={true}
    />
  );
};
```

### Using Custom Hooks

```tsx
import { useProduct, useRelatedProducts } from '@/hooks/useProducts';

const ProductDetail = ({ slug }: { slug: string }) => {
  const { data: product, isLoading } = useProduct(slug);
  const { data: relatedProducts } = useRelatedProducts(
    product?.id || 0,
    product?.attributes.category || '',
    product?.attributes.brandName,
    4
  );
  
  // Component implementation
};
```

## 🎨 Styling and Branding

The components use Nile Pro's brand colors:

- **Primary Blue**: #277BD8
- **Dark Blue**: #1564A9
- **Golden Yellow**: #FBB040
- **Red**: #E31B23
- **Black**: #1A1A1A
- **White**: #FFFFFF

## 🚀 Performance Features

- **Lazy Loading**: Images and components load on demand
- **Code Splitting**: Optimized bundle sizes
- **Caching**: React Query caching with 5-15 minute TTL
- **Image Optimization**: WebP support and responsive images
- **Performance Monitoring**: Web Vitals tracking

## 🔄 Migration from Static Data

The old static components are still available for backward compatibility:

- Old: `ProductCard` from `@/components/product`
- New: `ModernProductCard` from `@/components/product`

The routing has been updated to use the new modern components:
- `/products` → `ModernProductsPage`
- `/products/:slug` → `ModernProductDetail`

## 📚 API Documentation

### Product Service Methods

```typescript
// Get products with filtering
const products = await productService.getProducts({
  category: 'air-handling-unit',
  brand: 'ACS Klima',
  featured: true,
  search: 'heat pump',
  sort: 'name',
  order: 'asc',
  limit: 20
});

// Get single product
const product = await productService.getProductBySlug('acs-klima-ahu-2500');

// Submit quote request
const result = await productService.submitQuoteRequest({
  name: 'John Doe',
  email: '<EMAIL>',
  productId: 1,
  productName: 'ACS Klima AHU-2500',
  message: 'Interested in pricing for 5 units'
});
```

## 🐛 Troubleshooting

### Common Issues

1. **API 404 Errors (Most Common Issue)**:
   - **Problem**: Getting 404 errors on `/api/products` even though Strapi is running
   - **Solution**: Set up API permissions in Strapi Admin Panel
   - **Steps**:
     1. Go to http://localhost:1337/admin
     2. Settings > Users & Permissions Plugin > Roles > Public
     3. Enable `find` and `findOne` for Product and Brand
     4. Enable `create` for Quote-request
     5. Click Save

2. **Framer Motion Import Error**:
   - **Fixed**: Framer Motion has been removed and replaced with CSS animations
   - All components now use CSS transitions instead of framer-motion

3. **Missing Dependencies**:
   - Run `npm install` to ensure all dependencies are installed
   - Key dependencies: `@tanstack/react-query`, `react-hook-form`, `axios`

4. **Strapi not running**:
   - Make sure Strapi is running on port 1337
   - Check if the environment variable `VITE_STRAPI_URL` is set correctly

5. **No products showing**:
   - First, fix API permissions (issue #1 above)
   - Then use the seed function in the admin panel at `/admin`
   - Click "Seed Products" to create sample data

6. **Images not loading**:
   - Check Strapi media library and upload permissions
   - Verify image URLs in browser console

7. **API errors**:
   - Check browser console and Strapi logs
   - Verify Strapi is running and accessible
   - Most likely cause: Missing API permissions (see issue #1)

### Development Tips

1. Use React Query DevTools for debugging API calls
2. Check the browser console for performance metrics
3. Use the admin panel to manage content
4. Test responsive design on different screen sizes

### Quick Fix Commands

```bash
# Install missing dependencies
npm install

# Check for TypeScript errors
npx tsc --noEmit

# Start development server
npm run dev

# Start Strapi CMS
cd nile-pro-cms && npm run develop
```

## 📞 Support

For technical support or questions about the implementation:

1. Check the `tasks.md` file for detailed implementation notes
2. Review the `changelog.md` for recent changes
3. Check component documentation in the source files
4. Use the browser developer tools for debugging

---

**🎉 You're all set!** The modern Product Card and Product Details Pages are now ready for use with full Strapi CMS integration.
