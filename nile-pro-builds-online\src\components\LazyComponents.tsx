/**
 * Lazy Loading Components
 * Code-split components for better performance
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Loading fallback components
const ProductCardSkeleton = () => (
  <div className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
    <div className="aspect-[4/3] bg-gray-200" />
    <div className="p-5 space-y-3">
      <div className="h-4 bg-gray-200 rounded w-3/4" />
      <div className="h-3 bg-gray-200 rounded w-1/2" />
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 rounded" />
        <div className="h-3 bg-gray-200 rounded w-5/6" />
      </div>
      <div className="flex justify-between items-center">
        <div className="h-4 bg-gray-200 rounded w-1/3" />
        <div className="h-8 bg-gray-200 rounded w-20" />
      </div>
    </div>
  </div>
);

const ProductDetailSkeleton = () => (
  <div className="min-h-screen bg-gray-50 animate-pulse">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="h-4 bg-gray-200 rounded w-64 mb-8" />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <div className="aspect-[4/3] bg-gray-200 rounded-lg" />
        <div className="space-y-4">
          <div className="h-8 bg-gray-200 rounded w-3/4" />
          <div className="h-4 bg-gray-200 rounded w-1/2" />
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded" />
            <div className="h-4 bg-gray-200 rounded w-5/6" />
          </div>
          <div className="h-32 bg-gray-200 rounded" />
        </div>
      </div>
    </div>
  </div>
);

const ProductGridSkeleton = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {Array.from({ length: 8 }).map((_, index) => (
      <ProductCardSkeleton key={index} />
    ))}
  </div>
);

// Lazy loaded components
export const LazyModernProductCard = React.lazy(() => import('./product/ModernProductCard'));
export const LazyModernProductDetail = React.lazy(() => import('../pages/ModernProductDetail'));
export const LazyModernProductsPage = React.lazy(() => import('../pages/ModernProductsPage'));
export const LazyProductGrid = React.lazy(() => import('./product/ProductGrid'));
export const LazyModernProductImageGallery = React.lazy(() => import('./product/ModernProductImageGallery'));
export const LazyModernProductSpecifications = React.lazy(() => import('./product/ModernProductSpecifications'));
export const LazyQuoteForm = React.lazy(() => import('./product/QuoteForm'));

// Wrapper components with suspense
export const ModernProductCardWithSuspense = (props: any) => (
  <Suspense fallback={<ProductCardSkeleton />}>
    <LazyModernProductCard {...props} />
  </Suspense>
);

export const ModernProductDetailWithSuspense = (props: any) => (
  <Suspense fallback={<ProductDetailSkeleton />}>
    <LazyModernProductDetail {...props} />
  </Suspense>
);

export const ModernProductsPageWithSuspense = (props: any) => (
  <Suspense fallback={<ProductDetailSkeleton />}>
    <LazyModernProductsPage {...props} />
  </Suspense>
);

export const ProductGridWithSuspense = (props: any) => (
  <Suspense fallback={<ProductGridSkeleton />}>
    <LazyProductGrid {...props} />
  </Suspense>
);

export const ModernProductImageGalleryWithSuspense = (props: any) => (
  <Suspense fallback={<Skeleton className="aspect-[4/3] w-full" />}>
    <LazyModernProductImageGallery {...props} />
  </Suspense>
);

export const ModernProductSpecificationsWithSuspense = (props: any) => (
  <Suspense fallback={
    <div className="space-y-4">
      <Skeleton className="h-8 w-64" />
      <div className="space-y-2">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    </div>
  }>
    <LazyModernProductSpecifications {...props} />
  </Suspense>
);

export const QuoteFormWithSuspense = (props: any) => (
  <Suspense fallback={
    <div className="bg-white rounded-lg p-6 space-y-4">
      <Skeleton className="h-6 w-32" />
      <div className="space-y-3">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
      <Skeleton className="h-10 w-full" />
    </div>
  }>
    <LazyQuoteForm {...props} />
  </Suspense>
);

// Error boundary for lazy components
export class LazyComponentErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ComponentType }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const Fallback = this.props.fallback;
      if (Fallback) {
        return <Fallback />;
      }
      
      return (
        <div className="p-4 text-center text-red-600">
          <p>Something went wrong loading this component.</p>
          <button 
            onClick={() => this.setState({ hasError: false })}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for lazy loading with error boundary
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P>,
  LoadingComponent?: React.ComponentType,
  ErrorComponent?: React.ComponentType
) => {
  const LazyComponent = React.lazy(() => Promise.resolve({ default: Component }));
  
  return (props: P) => (
    <LazyComponentErrorBoundary fallback={ErrorComponent}>
      <Suspense fallback={LoadingComponent ? <LoadingComponent /> : <div>Loading...</div>}>
        <LazyComponent {...props} />
      </Suspense>
    </LazyComponentErrorBoundary>
  );
};

// Preload utility for lazy components
export const preloadComponent = (componentImport: () => Promise<any>) => {
  const componentImportWrapper = () => componentImport();
  componentImportWrapper();
};

// Preload all modern components
export const preloadModernComponents = () => {
  // Preload critical components
  preloadComponent(() => import('./product/ModernProductCard'));
  preloadComponent(() => import('./product/ProductGrid'));
  
  // Preload on user interaction
  const preloadOnInteraction = () => {
    preloadComponent(() => import('../pages/ModernProductDetail'));
    preloadComponent(() => import('./product/ModernProductImageGallery'));
    preloadComponent(() => import('./product/ModernProductSpecifications'));
    preloadComponent(() => import('./product/QuoteForm'));
    
    // Remove event listeners after first interaction
    document.removeEventListener('mouseenter', preloadOnInteraction);
    document.removeEventListener('touchstart', preloadOnInteraction);
  };
  
  // Preload on first user interaction
  document.addEventListener('mouseenter', preloadOnInteraction, { once: true });
  document.addEventListener('touchstart', preloadOnInteraction, { once: true });
};
