<svg width="200" height="80" viewBox="0 0 200 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="80" fill="white"/>
  
  <!-- Background gradient -->
  <defs>
    <linearGradient id="hirefGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Company name -->
  <text x="20" y="35" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#hirefGradient)">HiRef</text>
  <text x="20" y="55" font-family="Arial, sans-serif" font-size="12" font-weight="normal" fill="#64748b">Precision Cooling</text>
  
  <!-- Cooling/Data center icon -->
  <g transform="translate(130, 20)">
    <!-- Server racks -->
    <rect x="0" y="0" width="12" height="30" fill="#dc2626" rx="2"/>
    <rect x="16" y="0" width="12" height="30" fill="#ef4444" rx="2"/>
    <rect x="32" y="0" width="12" height="30" fill="#dc2626" rx="2"/>
    
    <!-- Cooling lines -->
    <path d="M0 35 Q22 40 44 35" stroke="#3b82f6" stroke-width="2" fill="none"/>
    <path d="M4 38 Q22 43 40 38" stroke="#60a5fa" stroke-width="1.5" fill="none"/>
  </g>
  
  <!-- Country -->
  <text x="20" y="70" font-family="Arial, sans-serif" font-size="10" fill="#94a3b8">Italy</text>
</svg>
