'use strict';

/**
 * product router with custom import routes
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

const defaultRouter = createCoreRouter('api::product.product');

const customRoutes = {
  routes: [
    {
      method: 'POST',
      path: '/products/import',
      handler: 'product.importProduct',
    },
    {
      method: 'GET',
      path: '/products/import-sources',
      handler: 'product.importSources',
    },
    {
      method: 'POST',
      path: '/products/seed',
      handler: 'product.seed',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'DELETE',
      path: '/products/clear',
      handler: 'product.clear',
      config: {
        policies: [],
        middlewares: [],
      },
    }
  ]
};

module.exports = {
  routes: [
    ...defaultRouter.routes,
    ...customRoutes.routes
  ]
};
